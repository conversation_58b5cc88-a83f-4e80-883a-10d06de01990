# Pivot Point Strategy Configuration - Fyers API Version

# General Settings
general:
  env_path: "C:/Users/<USER>/Desktop/Python/.env"
  output_dir: "reports"

# Trading Symbols
symbols:
  - NIFTY
  #- BANKNIFTY
  #- FINNIFTY

# Data Fetcher Settings
data_fetcher:
  candle_interval: 5  # Options: 1, 3, 5, 15, 30, 60 (minutes)
  historical_days: 30
  refresh_interval: 300  # Seconds

# Pivot Point Settings
pivot_calculator:
  calculation_type: "WEEKLY"  # Options: "DAILY", "WEEKLY", "MONTHLY"
  lookback_weeks: 4
  proximity_percentage: 2.0

# Delta Filter Settings
delta_filter:
  min_delta: 0.30
  max_delta: 0.65
  max_ce_options: 25
  max_pe_options: 25

# Pivot Premium Filter Settings
pivot_premium_filter:
  min_volume: 1000
  min_price: 2.0
  deviation_percentage: 5.0
  min_open_interest: 500
  max_options_per_type: 15
  prioritize_high_volume: true

shortlist_engine:
  max_final_options: 10
  min_combined_score: 0.5
  prioritize_atm_options: true
  scoring_weights:
    delta_score: 0.3
    liquidity_score: 0.3
    pivot_proximity_score: 0.2
    volume_score: 0.2

trade_closer:
  enable_live_trading: false
  min_profit_threshold: 50
  max_loss_threshold: -500

# Strategy Execution Settings
strategy_execution:
  analysis_time: "09:45"
  closing_time: "15:06"
  lot_sizes:
    NIFTY: 50
    BANKNIFTY: 15
    FINNIFTY: 40

# Fyers API Settings
fyers:
  client_id: "SD0YWXHE6D-100"
  redirect_uri: "https://trade.fyers.in/api-login/redirect-uri/index.html"
  log_path: "logs"
