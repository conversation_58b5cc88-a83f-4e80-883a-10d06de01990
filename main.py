#!/usr/bin/env python3
"""
Main entry point for the Pivot Point Strategy Application.
"""

import sys
import os
import argparse
import asyncio
import signal
from pathlib import Path
from datetime import datetime, time
from typing import Optional, List
import logging
import pytz

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import configuration management
from config.config_manager import ConfigManager

# Import existing modules
from fyers_connect import FyersConnect
from pivot_points import pivot_point_standard
from utils.logging_config import setup_logging

# Import new modules
from modules.data_fetcher import DataFetcher
from modules.pivot_calculator import PivotCalculator
from modules.delta_filter import DeltaFilter
from modules.pivot_premium_filter import PivotPremiumFilter
from modules.shortlist_engine import ShortlistEngine
from modules.strategy_execution import StrategyExecutionEngine
from modules.trade_closer import TradeCloser

logger = logging.getLogger(__name__)


class PivotStrategyApplication:
    """
    Main application class that orchestrates all components of the pivot point strategy.
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the application with configuration.
        
        Args:
            config_path: Path to the configuration file
        """
        self.config_path = config_path
        self.config_manager: Optional[ConfigManager] = None
        self.is_running = False
        self.components = {}

        # IST timezone
        self.ist_tz = pytz.timezone('Asia/Kolkata')
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.is_running = False
    
    async def initialize(self) -> bool:
        """
        Initialize all application components.
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            logger.info("Initializing Pivot Strategy Application...")
            
            # Load configuration
            self.config_manager = ConfigManager()
            
            # Configuration is automatically loaded and validated
            
            # Setup enhanced logging based on configuration
            # await self._setup_logging()
            
            # Initialize core components
            await self._initialize_components()
            
            logger.info("Application initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize application: {e}")
            return False
    
    async def _setup_logging(self):
        """Setup enhanced logging system."""
        try:
            logging_config = self.config_manager.logging_config
            # setup_enhanced_logging(logging_config)
            logger.info("Enhanced logging system initialized")
        except Exception as e:
            logger.error(f"Failed to setup logging: {e}")
            raise
    
    async def _initialize_components(self):
        """Initialize all application components."""
        try:
            # Initialize Fyers connection
            fyers_config = self.config_manager.get_fyers_config()
            self.components['fyers_client'] = FyersConnect(
                client_id=fyers_config['client_id'],
                secret_key=fyers_config['secret_key'],
                redirect_uri=fyers_config['redirect_uri'],
                response_type=fyers_config['response_type'],
                grant_type=fyers_config['grant_type']
            )

            # Initialize data fetcher
            self.components['data_fetcher'] = DataFetcher(
                self.config_manager.get('data_fetcher', {}),
                self.components['fyers_client']
            )

            # Initialize analysis components
            self.components['pivot_calculator'] = PivotCalculator(
                self.config_manager.get('pivot_calculator', {})
            )
            self.components['delta_filter'] = DeltaFilter(
                self.config_manager.get('delta_filter', {})
            )
            self.components['pivot_premium_filter'] = PivotPremiumFilter(
                self.config_manager.get('pivot_premium_filter', {})
            )
            self.components['shortlist_engine'] = ShortlistEngine(
                self.config_manager.get('shortlist_engine', {})
            )

            # Initialize strategy components
            self.components['execution_engine'] = StrategyExecutionEngine(
                self.config_manager.get('strategy_execution', {}),
                self.components['fyers_client']
            )
            self.components['trade_closer'] = TradeCloser(
                self.config_manager.get('trade_closer', {}),
                self.components['fyers_client']
            )

            logger.info("All components initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
            raise
    
    async def run(self) -> None:
        """
        Main application run loop.
        """
        try:
            logger.info("Starting Pivot Strategy Application...")
            self.is_running = True
            
            # Get active symbols from configuration
            active_symbols = self.config_manager.get_symbols()
            logger.info(f"Trading symbols: {active_symbols}")
            
            # Main execution loop
            while self.is_running:
                try:
                    # Check if market is open
                    if not self._is_market_time():
                        logger.info("Market is closed, waiting...")
                        await asyncio.sleep(60)  # Check every minute
                        continue
                    
                    # Execute main strategy workflow
                    await self._execute_strategy_workflow(active_symbols)
                    
                    # Wait for next execution cycle
                    refresh_interval = self.config_manager.get('general', {}).get('refresh_interval', 300)
                    await asyncio.sleep(refresh_interval)
                    
                except Exception as e:
                    logger.error(f"Error in main execution loop: {e}")
                    # Use error handler for recovery
                    # await self.components['error_handler'].handle_error(e)
                    await asyncio.sleep(30)  # Wait before retrying
            
            logger.info("Application stopped")
            
        except Exception as e:
            logger.error(f"Critical error in main run loop: {e}")
            raise
        finally:
            await self._cleanup()
    
    def _is_market_time(self) -> bool:
        """
        Check if current time is within market hours.
        
        Returns:
            bool: True if market is open, False otherwise
        """
        try:
            now = datetime.now(self.ist_tz).time()
            market_start = time(9, 15)  # 9:15 AM IST
            market_end = time(15, 30)   # 3:30 PM IST

            return market_start <= now <= market_end

        except Exception as e:
            logger.error(f"Error checking market time: {e}")
            return False
    
    async def _execute_strategy_workflow(self, symbols: List[str]) -> None:
        """
        Execute the main strategy workflow for given symbols.

        Args:
            symbols: List of symbols to process
        """
        try:
            logger.info("Executing strategy workflow...")

            # Check if it's execution time (9:45 AM)
            current_time = datetime.now(self.ist_tz).time()
            execution_time = time(9, 45)
            closing_time = time(15, 6)

            if execution_time <= current_time <= time(9, 50):
                await self._execute_morning_strategy(symbols)
            elif closing_time <= current_time <= time(15, 16):
                await self._execute_closing_strategy()
            else:
                await self._run_analysis_only(symbols)

        except Exception as e:
            logger.error(f"Error in strategy workflow: {e}")
            raise

    async def _execute_morning_strategy(self, symbols: List[str]) -> None:
        """Execute the morning strategy at 9:45 AM."""
        try:
            logger.info("Executing morning strategy (9:45 AM)")

            for symbol in symbols:
                logger.info(f"Processing symbol: {symbol}")

                # Step 1: Fetch market data
                historical_data = await self.components['data_fetcher'].fetch_historical_candles(symbol)
                options_data = await self.components['data_fetcher'].fetch_options_chain(symbol)
                current_price = await self.components['data_fetcher'].fetch_current_spot_price(symbol)

                # Step 2: Calculate pivot points
                pivot_analysis = self.components['pivot_calculator'].get_comprehensive_pivot_analysis(
                    historical_data, current_price
                )

                # Step 3: Apply delta filter
                delta_filtered = self.components['delta_filter'].apply_comprehensive_filter(
                    options_data, current_price
                )

                # Step 4: Apply pivot premium filter
                pivot_filtered = self.components['pivot_premium_filter'].apply_comprehensive_filter(
                    options_data, pivot_analysis.get('current_pivots', {}), current_price
                )

                # Step 5: Consolidate and create shortlist
                consolidated_options = self.components['shortlist_engine'].consolidate_filtered_options(
                    delta_filtered, pivot_filtered
                )

                scored_options = self.components['shortlist_engine'].calculate_comprehensive_scores(
                    consolidated_options, current_price
                )

                final_shortlist = self.components['shortlist_engine'].create_final_shortlist(scored_options)

                # Step 6: Analyze market conditions and execute strategy
                market_analysis = self.components['execution_engine'].analyze_market_conditions(
                    final_shortlist, pivot_analysis, current_price
                )

                execution_results = await self.components['execution_engine'].execute_strategy(market_analysis)

                logger.info(f"Completed morning strategy for {symbol}")
                logger.info(f"Final shortlist: {final_shortlist.get('total_shortlisted', 0)} options")
                logger.info(f"Executed trades: {execution_results.get('total_executed', 0)}")

        except Exception as e:
            logger.error(f"Error in morning strategy execution: {e}")
            raise

    async def _execute_closing_strategy(self) -> None:
        """Execute the closing strategy at 3:06 PM."""
        try:
            logger.info("Executing closing strategy (3:06 PM)")

            # Get active positions
            active_positions = self.components['execution_engine'].get_active_positions()

            # Close all positions
            closing_results = await self.components['trade_closer'].close_all_positions(active_positions)

            # Generate daily report
            daily_report = self.components['trade_closer'].generate_daily_trade_report()

            logger.info(f"Closing strategy completed")
            logger.info(f"Closed positions: {closing_results.get('total_closed', 0)}")
            logger.info(f"Daily P&L: {closing_results.get('daily_pnl', 0):.2f}")

        except Exception as e:
            logger.error(f"Error in closing strategy execution: {e}")
            raise

    async def _run_analysis_only(self, symbols: List[str]) -> None:
        """Run analysis without executing trades."""
        try:
            logger.info("Running analysis mode (no trade execution)")

            for symbol in symbols:
                logger.info(f"Analyzing symbol: {symbol}")

                # Fetch data and run analysis
                historical_data = await self.components['data_fetcher'].fetch_historical_candles(symbol)
                options_data = await self.components['data_fetcher'].fetch_options_chain(symbol)
                current_price = await self.components['data_fetcher'].fetch_current_spot_price(symbol)

                # Run complete analysis pipeline
                pivot_analysis = self.components['pivot_calculator'].get_comprehensive_pivot_analysis(
                    historical_data, current_price
                )

                delta_filtered = self.components['delta_filter'].apply_comprehensive_filter(
                    options_data, current_price
                )

                pivot_filtered = self.components['pivot_premium_filter'].apply_comprehensive_filter(
                    options_data, pivot_analysis.get('current_pivots', {}), current_price
                )

                consolidated_options = self.components['shortlist_engine'].consolidate_filtered_options(
                    delta_filtered, pivot_filtered
                )

                final_shortlist = self.components['shortlist_engine'].create_final_shortlist(
                    self.components['shortlist_engine'].calculate_comprehensive_scores(
                        consolidated_options, current_price
                    )
                )

                logger.info(f"Analysis completed for {symbol}")
                logger.info(f"Final shortlist: {final_shortlist.get('total_shortlisted', 0)} options")

        except Exception as e:
            logger.error(f"Error in analysis mode: {e}")
            raise
    

    
    async def _cleanup(self) -> None:
        """Cleanup resources and generate final reports."""
        try:
            logger.info("Cleaning up application resources...")

            # Close all open positions if any
            if 'trade_closer' in self.components and 'execution_engine' in self.components:
                active_positions = self.components['execution_engine'].get_active_positions()
                if active_positions.get('total_positions', 0) > 0:
                    await self.components['trade_closer'].close_all_positions(active_positions)

            # Generate final report
            if 'trade_closer' in self.components:
                daily_report = self.components['trade_closer'].generate_daily_trade_report()
                logger.info(f"Final daily report generated: {daily_report.get('summary', {})}")

            logger.info("Cleanup completed")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Enhanced Pivot Point Strategy Application"
    )
    parser.add_argument(
        "--config", 
        default="config.yaml",
        help="Path to configuration file"
    )
    parser.add_argument(
        "--mode",
        choices=["live", "simulation", "backtest"],
        default="live",
        help="Execution mode"
    )
    parser.add_argument(
        "--symbols",
        help="Comma-separated list of symbols to trade"
    )
    
    args = parser.parse_args()
    
    # Setup basic logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # Create and initialize application
        app = PivotStrategyApplication(args.config)
        
        if not await app.initialize():
            logger.error("Failed to initialize application")
            sys.exit(1)
        
        # Override symbols if provided via command line
        if args.symbols:
            symbols = [s.strip() for s in args.symbols.split(',')]
            logger.info(f"Using command line symbols: {symbols}")
        
        # Set execution mode
        if args.mode != "live":
            logger.info(f"Running in {args.mode} mode")
        
        # Run the application
        await app.run()
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
