"""
Data Fetcher Module for the Pivot Point Strategy Application.

This module provides configurable data fetching capabilities for:
1. Historical candle data (1-60 minute intervals)
2. Options chain data for NIFTY, BANKNIFTY, FINNIFTY
3. Current and past weeks options data
4. Real-time spot prices
"""

import logging
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from fyers_connect import FyersConnect

logger = logging.getLogger(__name__)


class DataFetcher:
    """
    Enhanced data fetcher that integrates with existing FyersConnect
    and provides configurable data retrieval capabilities.
    """
    
    def __init__(self, config: dict, fyers_client: Optional[FyersConnect] = None):
        """
        Initialize the data fetcher.
        
        Args:
            config: Configuration dictionary for data fetcher settings
            fyers_client: Optional FyersConnect instance
        """
        self.config = config
        self.fyers_client = fyers_client or FyersConnect()
        
        # Configuration settings
        self.candle_interval = config.get('candle_interval', 5)
        self.historical_days = config.get('historical_days', 30)
        self.refresh_interval = config.get('refresh_interval', 300)
        
        # Cache for data to avoid redundant API calls
        self._data_cache = {}
        self._cache_timestamps = {}
        
        logger.info(f"DataFetcher initialized with interval: {self.candle_interval}min, "
                   f"historical days: {self.historical_days}")
    
    def ensure_connection(self) -> bool:
        """
        Ensure Fyers API connection is active.
        
        Returns:
            bool: True if connected, False otherwise
        """
        try:
            if not self.fyers_client.fyers:
                logger.info("Fyers client not connected, attempting login...")
                return self.fyers_client.login()
            return True
        except Exception as e:
            logger.error(f"Error ensuring Fyers connection: {e}")
            return False
    
    def fetch_historical_candles(self, symbol: str, days: Optional[int] = None) -> pd.DataFrame:
        """
        Fetch historical candle data for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., 'NIFTY', 'BANKNIFTY')
            days: Number of days of historical data (optional, uses config default)
            
        Returns:
            pd.DataFrame: Historical OHLC data with timestamp index
        """
        try:
            if not self.ensure_connection():
                logger.error("Failed to establish Fyers connection")
                return pd.DataFrame()
            
            # Use provided days or config default
            fetch_days = days or self.historical_days
            
            # Check cache first
            cache_key = f"{symbol}_{self.candle_interval}_{fetch_days}"
            if self._is_cache_valid(cache_key):
                logger.info(f"Using cached data for {symbol}")
                return self._data_cache[cache_key]
            
            logger.info(f"Fetching {fetch_days} days of {self.candle_interval}min candles for {symbol}")
            
            # Calculate date range
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=fetch_days)
            
            # Fetch data using existing FyersConnect method
            df = self.fyers_client.get_ohlc_data(
                symbol=symbol,
                interval=str(self.candle_interval),
                start_date=start_date.strftime("%Y-%m-%d"),
                end_date=end_date.strftime("%Y-%m-%d")
            )
            
            if not df.empty:
                # Cache the data
                self._data_cache[cache_key] = df
                self._cache_timestamps[cache_key] = datetime.now()
                
                logger.info(f"Successfully fetched {len(df)} candles for {symbol}")
                logger.info(f"Date range: {df.index[0]} to {df.index[-1]}")
            else:
                logger.warning(f"No historical data received for {symbol}")
            
            return df
            
        except Exception as e:
            logger.error(f"Error fetching historical candles for {symbol}: {e}")
            return pd.DataFrame()
    
    def fetch_options_chain(self, symbol: str, expiry_type: str = "weekly") -> List[Dict[str, Any]]:
        """
        Fetch options chain data for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., 'NIFTY', 'BANKNIFTY')
            expiry_type: Type of expiry ('weekly' or 'monthly')
            
        Returns:
            List[Dict]: Options chain data
        """
        try:
            if not self.ensure_connection():
                logger.error("Failed to establish Fyers connection")
                return []
            
            # Check cache first
            cache_key = f"options_{symbol}_{expiry_type}"
            if self._is_cache_valid(cache_key, cache_duration=60):  # 1 minute cache for options
                logger.info(f"Using cached options data for {symbol}")
                return self._data_cache[cache_key]
            
            logger.info(f"Fetching {expiry_type} options chain for {symbol}")
            
            # Fetch options chain using existing FyersConnect method
            options_data = self.fyers_client.get_option_chain(
                symbol=symbol,
                expiry_type=expiry_type,
                monthly_expiry="current"
            )
            
            if options_data:
                # Cache the data
                self._data_cache[cache_key] = options_data
                self._cache_timestamps[cache_key] = datetime.now()
                
                logger.info(f"Successfully fetched {len(options_data)} option strikes for {symbol}")
            else:
                logger.warning(f"No options data received for {symbol}")
            
            return options_data
            
        except Exception as e:
            logger.error(f"Error fetching options chain for {symbol}: {e}")
            return []
    
    def fetch_current_spot_price(self, symbol: str) -> float:
        """
        Fetch current spot price for a symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            float: Current spot price
        """
        try:
            if not self.ensure_connection():
                logger.error("Failed to establish Fyers connection")
                return 0.0
            
            # Check cache first (very short cache for spot prices)
            cache_key = f"spot_{symbol}"
            if self._is_cache_valid(cache_key, cache_duration=30):  # 30 seconds cache
                return self._data_cache[cache_key]
            
            logger.info(f"Fetching current spot price for {symbol}")
            
            # Fetch spot price using existing FyersConnect method
            spot_price = self.fyers_client.get_spot_price(symbol)
            
            if spot_price > 0:
                # Cache the data
                self._data_cache[cache_key] = spot_price
                self._cache_timestamps[cache_key] = datetime.now()
                
                logger.info(f"Current spot price for {symbol}: {spot_price:.2f}")
            else:
                logger.warning(f"Invalid spot price received for {symbol}: {spot_price}")
            
            return spot_price
            
        except Exception as e:
            logger.error(f"Error fetching spot price for {symbol}: {e}")
            return 0.0
    
    def fetch_weekly_pivot_data(self, symbol: str) -> Dict[str, Any]:
        """
        Fetch weekly OHLC data for pivot point calculations.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Dict: Weekly OHLC data
        """
        try:
            if not self.ensure_connection():
                logger.error("Failed to establish Fyers connection")
                return {}
            
            # Check cache first
            cache_key = f"weekly_pivot_{symbol}"
            if self._is_cache_valid(cache_key, cache_duration=3600):  # 1 hour cache
                logger.info(f"Using cached weekly pivot data for {symbol}")
                return self._data_cache[cache_key]
            
            logger.info(f"Fetching weekly pivot data for {symbol}")
            
            # Fetch weekly pivot data using existing FyersConnect method
            weekly_data = self.fyers_client.get_weekly_pivot_ohlc_data(symbol)
            
            if weekly_data and weekly_data.get('high', 0) > 0:
                # Cache the data
                self._data_cache[cache_key] = weekly_data
                self._cache_timestamps[cache_key] = datetime.now()
                
                logger.info(f"Weekly pivot data for {symbol}: "
                           f"O:{weekly_data.get('open', 0):.2f} "
                           f"H:{weekly_data.get('high', 0):.2f} "
                           f"L:{weekly_data.get('low', 0):.2f} "
                           f"C:{weekly_data.get('close', 0):.2f}")
            else:
                logger.warning(f"Invalid weekly pivot data received for {symbol}")
            
            return weekly_data
            
        except Exception as e:
            logger.error(f"Error fetching weekly pivot data for {symbol}: {e}")
            return {}
    
    def fetch_multiple_symbols_data(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Fetch data for multiple symbols efficiently.
        
        Args:
            symbols: List of trading symbols
            
        Returns:
            Dict: Data for each symbol
        """
        results = {}
        
        for symbol in symbols:
            logger.info(f"Fetching data for {symbol}")
            
            results[symbol] = {
                'historical_candles': self.fetch_historical_candles(symbol),
                'options_chain': self.fetch_options_chain(symbol),
                'spot_price': self.fetch_current_spot_price(symbol),
                'weekly_pivot_data': self.fetch_weekly_pivot_data(symbol)
            }
            
            logger.info(f"Completed data fetch for {symbol}")
        
        return results
    
    def _is_cache_valid(self, cache_key: str, cache_duration: int = None) -> bool:
        """
        Check if cached data is still valid.
        
        Args:
            cache_key: Cache key to check
            cache_duration: Cache duration in seconds (uses refresh_interval if None)
            
        Returns:
            bool: True if cache is valid, False otherwise
        """
        if cache_key not in self._data_cache or cache_key not in self._cache_timestamps:
            return False
        
        duration = cache_duration or self.refresh_interval
        cache_age = (datetime.now() - self._cache_timestamps[cache_key]).total_seconds()
        
        return cache_age < duration
    
    def clear_cache(self):
        """Clear all cached data."""
        self._data_cache.clear()
        self._cache_timestamps.clear()
        logger.info("Data cache cleared")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        Get information about cached data.
        
        Returns:
            Dict: Cache information
        """
        cache_info = {}
        current_time = datetime.now()
        
        for key, timestamp in self._cache_timestamps.items():
            age_seconds = (current_time - timestamp).total_seconds()
            cache_info[key] = {
                'cached_at': timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                'age_seconds': int(age_seconds),
                'data_size': len(str(self._data_cache.get(key, '')))
            }
        
        return cache_info
