"""
Delta Filter Module for the Pivot Point Strategy Application.

This module provides delta-based filtering capabilities for options:
1. Filters options based on delta values (0.30-0.65 range)
2. Uses Black-Scholes model for accurate delta calculations
3. Prioritizes options closest to money within delta range
4. Excludes deep OTM and ITM options
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from option_pricing_helpers import calculate_option_delta

logger = logging.getLogger(__name__)


class DeltaFilter:
    """
    Delta filter for options based on Black-Scholes delta calculations.
    """
    
    def __init__(self, config: dict):
        """
        Initialize the delta filter.
        
        Args:
            config: Configuration dictionary for delta filter settings
        """
        self.config = config
        self.min_delta = config.get('min_delta', 0.30)
        self.max_delta = config.get('max_delta', 0.65)
        self.max_ce_options = config.get('max_ce_options', 25)
        self.max_pe_options = config.get('max_pe_options', 25)
        self.prioritize_closest_to_money = config.get('prioritize_closest_to_money', True)
        self.exclude_deep_otm = config.get('exclude_deep_otm', True)
        self.exclude_deep_itm = config.get('exclude_deep_itm', True)
        
        # Default Black-Scholes parameters
        self.risk_free_rate = 0.05  # 5% risk-free rate
        self.default_volatility = 0.20  # 20% default volatility
        
        logger.info(f"DeltaFilter initialized with range: {self.min_delta} - {self.max_delta}")
    
    def calculate_option_delta_bs(self, spot_price: float, strike: float, 
                                 time_to_expiry: float, option_type: str,
                                 volatility: Optional[float] = None) -> float:
        """
        Calculate option delta using Black-Scholes model.
        
        Args:
            spot_price: Current spot price
            strike: Strike price
            time_to_expiry: Time to expiry in years
            option_type: 'call' or 'put'
            volatility: Implied volatility (optional, uses default if None)
            
        Returns:
            float: Delta value
        """
        try:
            # Use provided volatility or default
            vol = volatility or self.default_volatility
            
            # Calculate delta using existing function
            delta = calculate_option_delta(
                S=spot_price,
                K=strike,
                T=time_to_expiry,
                r=self.risk_free_rate,
                sigma=vol,
                option_type=option_type.lower()
            )
            
            return delta
            
        except Exception as e:
            logger.error(f"Error calculating delta for {option_type} {strike}: {e}")
            return 0.0
    
    def calculate_time_to_expiry(self, expiry_date: datetime) -> float:
        """
        Calculate time to expiry in years.
        
        Args:
            expiry_date: Option expiry date
            
        Returns:
            float: Time to expiry in years
        """
        try:
            now = datetime.now()
            if expiry_date <= now:
                return 0.01  # Minimum time to avoid division by zero
            
            time_diff = expiry_date - now
            days_to_expiry = time_diff.total_seconds() / (24 * 3600)
            years_to_expiry = days_to_expiry / 365.25
            
            return max(0.01, years_to_expiry)  # Minimum 0.01 years
            
        except Exception as e:
            logger.error(f"Error calculating time to expiry: {e}")
            return 0.01
    
    def filter_options_by_delta(self, options_data: List[Dict[str, Any]], 
                               spot_price: float) -> Dict[str, List[Dict[str, Any]]]:
        """
        Filter options based on delta criteria.
        
        Args:
            options_data: List of option data dictionaries
            spot_price: Current spot price
            
        Returns:
            Dict: Filtered options separated by type ('calls' and 'puts')
        """
        try:
            filtered_calls = []
            filtered_puts = []
            
            logger.info(f"Filtering {len(options_data)} options with spot price: {spot_price:.2f}")
            
            for option in options_data:
                strike = option.get('strike', 0)
                if strike <= 0:
                    continue
                
                # Get expiry date
                expiry_date = option.get('expiry_date')
                if not expiry_date:
                    logger.warning(f"No expiry date for strike {strike}, skipping")
                    continue
                
                # Calculate time to expiry
                if isinstance(expiry_date, str):
                    try:
                        expiry_date = datetime.strptime(expiry_date, '%Y-%m-%d')
                    except ValueError:
                        logger.warning(f"Invalid expiry date format for strike {strike}")
                        continue
                
                time_to_expiry = self.calculate_time_to_expiry(expiry_date)
                
                # Process call option
                if option.get('call_symbol') and option.get('call_ltp', 0) > 0:
                    call_delta = self.calculate_option_delta_bs(
                        spot_price, strike, time_to_expiry, 'call'
                    )
                    
                    # Apply delta filter
                    if self.min_delta <= abs(call_delta) <= self.max_delta:
                        # Apply additional filters
                        if self._should_include_option(option, 'call', spot_price, call_delta):
                            call_option = option.copy()
                            call_option['calculated_delta'] = call_delta
                            call_option['time_to_expiry'] = time_to_expiry
                            call_option['distance_from_spot'] = abs(strike - spot_price)
                            filtered_calls.append(call_option)
                
                # Process put option
                if option.get('put_symbol') and option.get('put_ltp', 0) > 0:
                    put_delta = self.calculate_option_delta_bs(
                        spot_price, strike, time_to_expiry, 'put'
                    )
                    
                    # Apply delta filter (use absolute value for puts)
                    if self.min_delta <= abs(put_delta) <= self.max_delta:
                        # Apply additional filters
                        if self._should_include_option(option, 'put', spot_price, put_delta):
                            put_option = option.copy()
                            put_option['calculated_delta'] = put_delta
                            put_option['time_to_expiry'] = time_to_expiry
                            put_option['distance_from_spot'] = abs(strike - spot_price)
                            filtered_puts.append(put_option)
            
            # Sort by distance from spot price if prioritizing closest to money
            if self.prioritize_closest_to_money:
                filtered_calls.sort(key=lambda x: x['distance_from_spot'])
                filtered_puts.sort(key=lambda x: x['distance_from_spot'])
            
            # Limit number of options
            filtered_calls = filtered_calls[:self.max_ce_options]
            filtered_puts = filtered_puts[:self.max_pe_options]
            
            logger.info(f"Delta filter results: {len(filtered_calls)} calls, {len(filtered_puts)} puts")
            
            return {
                'calls': filtered_calls,
                'puts': filtered_puts
            }
            
        except Exception as e:
            logger.error(f"Error filtering options by delta: {e}")
            return {'calls': [], 'puts': []}
    
    def _should_include_option(self, option: Dict[str, Any], option_type: str, 
                              spot_price: float, delta: float) -> bool:
        """
        Apply additional filters to determine if option should be included.
        
        Args:
            option: Option data dictionary
            option_type: 'call' or 'put'
            spot_price: Current spot price
            delta: Calculated delta
            
        Returns:
            bool: True if option should be included
        """
        try:
            strike = option.get('strike', 0)
            
            # Check for deep OTM options
            if self.exclude_deep_otm:
                if option_type == 'call' and strike > spot_price * 1.1:  # 10% OTM
                    return False
                elif option_type == 'put' and strike < spot_price * 0.9:  # 10% OTM
                    return False
            
            # Check for deep ITM options
            if self.exclude_deep_itm:
                if option_type == 'call' and strike < spot_price * 0.9:  # 10% ITM
                    return False
                elif option_type == 'put' and strike > spot_price * 1.1:  # 10% ITM
                    return False
            
            # Check minimum LTP
            ltp_key = f"{option_type}_ltp"
            ltp = option.get(ltp_key, 0)
            if ltp < 1.0:  # Minimum premium of 1.0
                return False
            
            # Check minimum volume
            volume_key = f"{option_type}_volume"
            volume = option.get(volume_key, 0)
            if volume < 100:  # Minimum volume
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error in additional option filters: {e}")
            return False
    
    def get_delta_statistics(self, filtered_options: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        Get statistics about filtered options.
        
        Args:
            filtered_options: Filtered options dictionary
            
        Returns:
            Dict: Delta statistics
        """
        try:
            stats = {
                'total_calls': len(filtered_options.get('calls', [])),
                'total_puts': len(filtered_options.get('puts', [])),
                'call_delta_range': {},
                'put_delta_range': {}
            }
            
            # Calculate call delta statistics
            calls = filtered_options.get('calls', [])
            if calls:
                call_deltas = [opt['calculated_delta'] for opt in calls]
                stats['call_delta_range'] = {
                    'min': round(min(call_deltas), 3),
                    'max': round(max(call_deltas), 3),
                    'avg': round(np.mean(call_deltas), 3)
                }
            
            # Calculate put delta statistics
            puts = filtered_options.get('puts', [])
            if puts:
                put_deltas = [abs(opt['calculated_delta']) for opt in puts]
                stats['put_delta_range'] = {
                    'min': round(min(put_deltas), 3),
                    'max': round(max(put_deltas), 3),
                    'avg': round(np.mean(put_deltas), 3)
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error calculating delta statistics: {e}")
            return {}
    
    def get_options_near_delta(self, filtered_options: Dict[str, List[Dict[str, Any]]], 
                              target_delta: float, tolerance: float = 0.05) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get options near a specific delta value.
        
        Args:
            filtered_options: Filtered options dictionary
            target_delta: Target delta value
            tolerance: Delta tolerance
            
        Returns:
            Dict: Options near target delta
        """
        try:
            near_delta_calls = []
            near_delta_puts = []
            
            # Filter calls near target delta
            for call in filtered_options.get('calls', []):
                delta = call.get('calculated_delta', 0)
                if abs(delta - target_delta) <= tolerance:
                    near_delta_calls.append(call)
            
            # Filter puts near target delta (use absolute value)
            for put in filtered_options.get('puts', []):
                delta = abs(put.get('calculated_delta', 0))
                if abs(delta - target_delta) <= tolerance:
                    near_delta_puts.append(put)
            
            logger.info(f"Found {len(near_delta_calls)} calls and {len(near_delta_puts)} puts "
                       f"near delta {target_delta} ± {tolerance}")
            
            return {
                'calls': near_delta_calls,
                'puts': near_delta_puts
            }
            
        except Exception as e:
            logger.error(f"Error finding options near delta {target_delta}: {e}")
            return {'calls': [], 'puts': []}
    
    def update_config(self, new_config: dict):
        """
        Update filter configuration.
        
        Args:
            new_config: New configuration dictionary
        """
        try:
            self.config.update(new_config)
            self.min_delta = self.config.get('min_delta', self.min_delta)
            self.max_delta = self.config.get('max_delta', self.max_delta)
            self.max_ce_options = self.config.get('max_ce_options', self.max_ce_options)
            self.max_pe_options = self.config.get('max_pe_options', self.max_pe_options)
            
            logger.info(f"Delta filter configuration updated: {self.min_delta} - {self.max_delta}")
            
        except Exception as e:
            logger.error(f"Error updating delta filter configuration: {e}")
    
    def get_filter_summary(self) -> Dict[str, Any]:
        """
        Get summary of current filter settings.
        
        Returns:
            Dict: Filter summary
        """
        return {
            'min_delta': self.min_delta,
            'max_delta': self.max_delta,
            'max_ce_options': self.max_ce_options,
            'max_pe_options': self.max_pe_options,
            'prioritize_closest_to_money': self.prioritize_closest_to_money,
            'exclude_deep_otm': self.exclude_deep_otm,
            'exclude_deep_itm': self.exclude_deep_itm,
            'risk_free_rate': self.risk_free_rate,
            'default_volatility': self.default_volatility
        }
