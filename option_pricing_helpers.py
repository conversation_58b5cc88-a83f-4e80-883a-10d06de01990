"""
Helper functions for option pricing calculations.
"""
import logging
import numpy as np
from scipy.stats import norm

# Configure logging
logger = logging.getLogger(__name__)

def calculate_d1(S, K, T, r, sigma):
    """
    Calculate d1 parameter for Black-Scholes formula

    Parameters:
        S: float - Current stock price
        K: float - Strike price
        T: float - Time to expiration (in years)
        r: float - Risk-free interest rate
        sigma: float - Volatility

    Returns:
        float - d1 value
    """
    if sigma <= 0 or T <= 0:
        return 0  # Avoid division by zero or negative values

    return (np.log(S/K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))

def calculate_d2(d1, sigma, T):
    """
    Calculate d2 parameter for Black-Scholes formula

    Parameters:
        d1: float - d1 value
        sigma: float - Volatility
        T: float - Time to expiration (in years)

    Returns:
        float - d2 value
    """
    if sigma <= 0 or T <= 0:
        return 0  # Avoid division by zero or negative values

    return d1 - sigma * np.sqrt(T)

def calculate_option_delta(S, K, T, r, sigma, option_type='call'):
    """
    Calculate the delta of an option using Black-Scholes formula

    Parameters:
        S: float - Current stock price
        K: float - Strike price
        T: float - Time to expiration (in years)
        r: float - Risk-free interest rate
        sigma: float - Volatility
        option_type: str - 'call' or 'put'

    Returns:
        float - Delta value (between 0 and 1 for calls, between -1 and 0 for puts)
    """
    # Handle edge cases
    if T <= 0:  # Expired option
        if option_type.lower() == 'call':
            return 1.0 if S > K else 0.0
        else:  # put
            return -1.0 if S < K else 0.0

    if sigma <= 0:  # No volatility
        if option_type.lower() == 'call':
            return 1.0 if S > K else 0.0
        else:  # put
            return -1.0 if S < K else 0.0

    # Calculate d1
    d1 = calculate_d1(S, K, T, r, sigma)

    # Calculate delta
    if option_type.lower() == 'call':
        return float(np.clip(np.exp(-r * T) * norm.cdf(d1), 0.0, 1.0))
    else:  # put
        return float(np.clip(np.exp(-r * T) * (norm.cdf(d1) - 1.0), -1.0, 0.0))
