
"""
Module for Fyers API integration.
Handles authentication, data fetching, and market data operations.
"""
import os
import json
import logging
import time
import pytz
import re  # Add regex import
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

import pandas as pd
from fyers_apiv3 import fyersModel
from fyers_apiv3.FyersWebsocket.data_ws import FyersDataSocket

# Configure logging
logger = logging.getLogger(__name__)

class FyersConnect:
    # Mapping of intervals to Fyers API resolutions
    INTERVAL_MAP = {
        "1D": "1D",   # Daily candle
        "1W": "1D",   # For weekly, we'll fetch daily data and aggregate
        "1": "1",     # 1 minute
        "3": "3",     # 3 minutes
        "5": "5",     # 5 minutes
        "15": "15",   # 15 minutes
        "30": "30",   # 30 minutes
        "60": "60"    # 1 hour
    }

    def __init__(self, fyers_config=None):
        """
        Initialize Fyers API connection.

        Args:
            fyers_config: FyersConfig instance. If None, a new instance will be created.
        """
        from config import FyersConfig
        self.config = fyers_config or FyersConfig()
        self.access_token = None
        self.fyers = None
        self.ws = None

    def login(self) -> bool:
        """
        Authenticate with Fyers API.

        Returns:
            bool: True if login successful, False otherwise
        """
        try:
            print("DEBUG: FyersConnect.login() called")
            # Check if we have existing valid tokens from today
            if os.path.exists(self.config.access_token_file) and os.path.exists(self.config.client_id_file):
                token_mtime = datetime.fromtimestamp(os.path.getmtime(self.config.access_token_file))
                print(f"DEBUG: Found token files. Last modified: {token_mtime}")

                if token_mtime.date() == datetime.now().date():
                    print("DEBUG: Tokens are from today, attempting to use them...")
                    logger.info("Found existing tokens from today, attempting to use them...")

                    try:
                        # Read existing tokens
                        with open(self.config.client_id_file, 'r') as f:
                            client_id = f.read().strip()
                        with open(self.config.access_token_file, 'r') as f:
                            self.access_token = f.read().strip()
                        
                        print(f"DEBUG: Read client_id: {client_id[:5]}... and access_token: {self.access_token[:10]}...")

                        # Initialize Fyers model with existing token
                        print("DEBUG: Initializing FyersModel with existing token...")
                        self.fyers = fyersModel.FyersModel(
                            client_id=client_id,
                            token=self.access_token,
                            log_path="logs"
                            # Removed token_type parameter which is causing errors
                        )

                        # Set required configuration
                        print("DEBUG: Setting configuration...")
                        self.fyers.set_configuration(
                            app_id=client_id,
                            access_token=self.access_token,
                            enable_option_chain=True  # Enable option chain access
                        )

                        # Verify connection
                        print("DEBUG: Verifying connection with get_profile()...")
                        profile = self.fyers.get_profile()
                        print(f"DEBUG: Profile response: {profile}")
                        if profile and profile.get("code") == 200:
                            print("DEBUG: Successfully logged in using existing token")
                            logger.info("Successfully logged in to Fyers API using existing token")
                            return True
                        else:
                            print(f"DEBUG: Existing token is invalid: {profile}")
                            logger.warning("Existing token is invalid, will request new one")
                    except Exception as e:
                        print(f"DEBUG: Error using existing token: {str(e)}")
                        logger.warning(f"Error using existing token: {str(e)}, will request new one")

            # If we get here, we need to perform fresh authentication
            print("DEBUG: Need to perform fresh authentication")
            print("DEBUG: Calling self.config.authenticate()...")
            if not self.config.authenticate():
                print("DEBUG: Authentication failed")
                return False

            # Read the newly obtained access token
            print("DEBUG: Reading newly obtained access token")
            with open(self.config.access_token_file, 'r') as f:
                self.access_token = f.read().strip()

            # Initialize Fyers model
            print("DEBUG: Initializing FyersModel with new token")
            self.fyers = fyersModel.FyersModel(
                client_id=self.config.config["client_id"],
                token=self.access_token,
                log_path="logs"
            )

            print("DEBUG: Successfully logged in with new token")
            logger.info("Successfully logged in to Fyers API with new token")
            return True
        except Exception as e:
            print(f"DEBUG: Error during Fyers login: {str(e)}")
            logger.error(f"Error during Fyers login: {str(e)}")
            return False

    def get_ohlc_data(self, symbol: str, interval: str = "1D",
                      start_date: Optional[str] = None,
                      end_date: Optional[str] = None) -> pd.DataFrame:
        """
        Fetch OHLC data for a symbol.

        Args:
            symbol: Trading symbol
            interval: Time interval (1D, 1W, etc.)
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format

        Returns:
            pd.DataFrame: OHLC data
        """
        try:
            if not self.fyers:
                raise Exception("Not logged in to Fyers API")

            # Format symbol for Fyers
            fyers_symbol = self._format_symbol(symbol)

            # Set default dates if not provided
            if not start_date:
                today = datetime.now()
                if interval == "1W":
                    # For weekly pivot, we need last week's last trading day
                    current_weekday = today.weekday()  # Monday is 0, Sunday is 6

                    # Calculate last Friday's date (as most common last trading day)
                    days_to_last_friday = (current_weekday + 3) % 7 + 7
                    last_trading_day = today - timedelta(days=days_to_last_friday)

                    # Try up to 3 days back from Friday (Friday, Thursday, Wednesday)
                    data = None
                    df = pd.DataFrame()

                    for days_back in range(3):
                        try_date = last_trading_day + timedelta(days=days_back)
                        current_date = try_date.strftime("%Y-%m-%d")
                        logger.info(f"Trying to fetch weekly pivot data for {current_date}")

                        data = {
                            "symbol": fyers_symbol,
                            "resolution": self.INTERVAL_MAP[interval],
                            "date_format": "1",
                            "range_from": current_date,
                            "range_to": current_date,
                            "cont_flag": "1"
                        }

                        response = self.fyers.history(data)
                        if response["code"] == 200:
                            df = pd.DataFrame(response["candles"],
                                          columns=["timestamp", "open", "high", "low", "close", "volume"])
                            if not df.empty:
                                df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s")
                                df.set_index("timestamp", inplace=True)
                                trading_day = df.iloc[0]

                                logger.info(f"Found data for {current_date}:")
                                logger.info(f"Open: {trading_day['open']:.2f}")
                                logger.info(f"High: {trading_day['high']:.2f}")
                                logger.info(f"Low: {trading_day['low']:.2f}")
                                logger.info(f"Close: {trading_day['close']:.2f}")
                                return df

                    if df.empty:
                        raise Exception("No trading day data found in last week")
                else:
                    # For other intervals, use configured days_to_fetch
                    days_to_fetch = self.config.config.get("timeframe", {}).get("days_to_fetch", 15)
                    start_date = (today - timedelta(days=days_to_fetch)).strftime("%Y-%m-%d")
                    end_date = today.strftime("%Y-%m-%d")

            # For non-weekly intervals or when specific dates are provided
            data = {
                "symbol": fyers_symbol,
                "resolution": self.INTERVAL_MAP[interval],
                "date_format": "1",
                "range_from": start_date,
                "range_to": end_date or start_date,
                "cont_flag": "1"
            }

            response = self.fyers.history(data)
            if response["code"] != 200:
                raise Exception(f"Failed to fetch OHLC data: {response}")

            df = pd.DataFrame(response["candles"],
                            columns=["timestamp", "open", "high", "low", "close", "volume"])
            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s")
            df.set_index("timestamp", inplace=True)
            return df

        except Exception as e:
            logger.error(f"Error fetching OHLC data: {str(e)}")
            return pd.DataFrame()

    def get_option_chain(self, symbol: str, expiry_type: str = "weekly", monthly_expiry: str = "both") -> List[Dict[str, Any]]:
        """
        Fetch option chain data for a symbol.

        Args:
            symbol: Trading symbol
            expiry_type: Type of expiry to fetch ('weekly' or 'monthly')
            monthly_expiry: Which monthly expiry to use ('current', 'next', 'both')

        Returns:
            List[Dict]: Option chain data
        """
        try:
            if not self.fyers:
                raise Exception("Not logged in to Fyers API")

            # Format symbol for spot price
            fyers_symbol = self._format_symbol(symbol)

            # Get expiry dates based on expiry_type
            today = datetime.now()
            expiry_dates = []

            if expiry_type.upper() == "WEEKLY":
                # Calculate next Thursday expiry for weekly
                days_to_thursday = (3 - today.weekday()) % 7  # 3 is Thursday
                if days_to_thursday == 0 and today.hour >= 15:  # After market hours on Thursday
                    days_to_thursday = 7  # Use next Thursday

                nearest_expiry = (today + timedelta(days=days_to_thursday)).date()
                expiry_dates.append(nearest_expiry)
                logger.info(f"Using weekly expiry date: {nearest_expiry.strftime('%Y-%m-%d')}")
            else:
                # For monthly expiry, find the last Thursday of current and/or next month
                current_date = today.date()
                current_month = current_date.month
                current_year = current_date.year

                # Get the last Thursday of the current month
                from data.option_utils import get_last_thursday_of_month
                last_thursday_current = get_last_thursday_of_month(current_year, current_month)

                # Get the last Thursday of the next month
                next_month = (current_month % 12) + 1
                next_year = current_year + (1 if current_month == 12 else 0)
                last_thursday_next = get_last_thursday_of_month(next_year, next_month)

                # Determine which months to include based on the monthly_expiry setting
                include_current = monthly_expiry.lower() in ["current", "both"]
                include_next = monthly_expiry.lower() in ["next", "both"]

                # Add current month if configured
                if include_current:
                    expiry_dates.append(last_thursday_current)
                    logger.info(f"Including current month's expiry: {last_thursday_current.strftime('%Y-%m-%d')}")

                # Add next month if configured
                if include_next:
                    expiry_dates.append(last_thursday_next)
                    logger.info(f"Including next month's expiry: {last_thursday_next.strftime('%Y-%m-%d')}")

            # Use the first expiry date for initial processing
            nearest_expiry = expiry_dates[0]
            logger.info(f"Using primary expiry date: {nearest_expiry.strftime('%Y-%m-%d')}")

            # Get spot price
            spot_response = self.fyers.quotes({"symbols": fyers_symbol})
            if spot_response["code"] != 200:
                raise Exception(f"Failed to fetch spot price: {spot_response}")

            spot_price = 0.0
            previous_close = 0.0
            if spot_response.get("d") and len(spot_response["d"]) > 0:
                spot_data = spot_response["d"][0]
                if "v" in spot_data:
                    spot_values = spot_data["v"]
                    spot_price = float(spot_values.get("lp", 0.0))
                    previous_close = float(spot_values.get("prev_close_price", 0.0))
                    logger.info(f"Spot price: {spot_price}")

            # Get strike interval from config
            from symbol_config import get_symbol_config
            symbol_config = get_symbol_config(symbol)
            strike_interval = symbol_config.get("strike_interval", 50)

            # Calculate strikes
            base_strike = round(spot_price / strike_interval) * strike_interval
            num_strikes = 10
            potential_strikes = [
                base_strike + (i - num_strikes) * strike_interval
                for i in range(num_strikes * 2 + 1)
            ]

            # Build option symbols list
            symbols_for_quote = []
            year = nearest_expiry.strftime('%y')
            month = str(nearest_expiry.month)  # Single digit month
            day = nearest_expiry.strftime('%d')

            # Import the option symbol formatter
            from utils.option_pricing import format_option_symbol
            
            for strike in potential_strikes:
                # Use the correct format for Fyers API: NSE:NIFTYYYMDD19000CE
                ce_symbol = format_option_symbol(symbol, nearest_expiry, int(strike), "CE")
                pe_symbol = format_option_symbol(symbol, nearest_expiry, int(strike), "PE")
                
                # Add symbols to the list
                symbols_for_quote.extend([ce_symbol, pe_symbol])
                
                logger.info(f"Added option symbols for strike {strike}: CE={ce_symbol}, PE={pe_symbol}")

            # Process in batches
            processed_data = []
            for i in range(0, len(symbols_for_quote), 50):
                batch = symbols_for_quote[i:i + 50]
                options_response = self.fyers.quotes({"symbols": batch})

                logger.info(f"Fetching quotes for batch of {len(batch)} symbols")

                if not isinstance(options_response, dict) or options_response.get("code") != 200:
                    logger.error(f"Invalid response from Fyers API for batch: {options_response}")
                    logger.error(f"Batch contained {len(batch)} symbols")
                    # Log the first few symbols in the batch for debugging
                    if batch:
                        logger.error(f"First few symbols in batch: {', '.join(batch[:min(5, len(batch))])}")
                    continue

                quote_data = options_response.get("d", [])
                if not quote_data:
                    logger.error(f"No quote data returned from Fyers API for batch of {len(batch)} symbols")
                    continue

                for quote in quote_data:
                    if not isinstance(quote, dict):
                        continue

                    symbol_name = quote.get("n", "")
                    if not symbol_name:
                        continue

                    # Clean up the symbol name - remove any extra characters
                    symbol_name = symbol_name.strip()
                    # Remove any unexpected characters like quotes, brackets, etc.
                    symbol_name = re.sub(r"[\[\]'\"]", "", symbol_name)
                    # Remove any trailing characters that aren't part of the symbol
                    symbol_name = re.sub(r"(NSE:[A-Z0-9]+(?:CE|PE)).*", r"\1", symbol_name)

                    logger.info(f"Processing symbol: {symbol_name}")

                    # Parse NSE:NIFTY255224800CE format
                    try:
                        # Split into exchange and symbol parts
                        parts = symbol_name.split(":")
                        if len(parts) != 2:
                            logger.warning(f"Invalid symbol format (no exchange separator): {symbol_name}")
                            continue

                        option_part = parts[1].strip()  # NIFTY255224800CE - remove any whitespace

                        # Extract the base symbol (e.g., NIFTY, BANKNIFTY)
                        base_symbol_match = re.match(r'^([A-Z]+)', option_part)
                        if not base_symbol_match:
                            logger.warning(f"Could not identify base symbol in: {option_part}")
                            continue

                        base_symbol = base_symbol_match.group(1)

                        # Extract option type from the end (CE or PE)
                        opt_type = option_part[-2:] if len(option_part) >= 2 else ""
                        if opt_type not in ("CE", "PE"):
                            logger.warning(f"Invalid option type in {option_part}: {opt_type}")
                            continue

                        # The middle part should contain the date and strike price
                        # First, remove the base symbol and option type
                        date_strike_part = option_part[len(base_symbol):-2]

                        # The first 5 characters should be the date (YYMDD format)
                        if len(date_strike_part) < 5:
                            logger.warning(f"Date-strike part too short in {option_part}: {date_strike_part}")
                            continue

                        date_part = date_strike_part[:5]
                        year = date_part[:2]
                        month = date_part[2:3]
                        day = date_part[3:5]

                        # The rest should be the strike price
                        strike_str = date_strike_part[5:]

                        # Make sure strike_str contains only digits
                        if not strike_str.isdigit():
                            logger.warning(f"Strike price contains non-digits in {option_part}: {strike_str}")
                            continue

                        # Convert strike to float
                        strike = float(strike_str)

                        logger.info(f"Successfully parsed: base={base_symbol}, year={year}, month={month}, "
                                   f"day={day}, strike={strike}, type={opt_type}")

                    except Exception as e:
                        logger.warning(f"Could not parse symbol '{symbol_name}': {e}")
                        continue

                    is_call = opt_type == 'CE'
                    option_values = quote.get("v", {})

                    # Get previous day's close for calculating change
                    # Use actual LTP from API, don't simulate
                    ltp = float(option_values.get('lp', 0.0))
                    
                    # Get previous close from API
                    prev_close = float(option_values.get('prev_close_price', 0.0))

                    # Calculate change
                    change = 0.0
                    if prev_close > 0:
                        change = ((ltp - prev_close) / prev_close) * 100.0

                    # Calculate time to expiry for reference
                    time_to_expiry = self._calculate_time_to_expiry(nearest_expiry)
                    
                    # We'll use a more accurate delta calculation based on Black-Scholes model
                    # For now, we'll use a simplified approximation
                    # In a future update, we can implement a proper Black-Scholes calculator
                    
                    # For call options: delta increases as spot price increases relative to strike
                    # For put options: delta decreases (becomes more negative) as spot price increases
                    if is_call:
                        # Simple delta approximation for calls: ranges from 0 to 1
                        # When spot = strike, delta ≈ 0.5
                        delta = max(0.0, min(1.0, 0.5 + (spot_price - strike) / (spot_price * 0.1)))
                    else:
                        # Simple delta approximation for puts: ranges from -1 to 0
                        # When spot = strike, delta ≈ -0.5
                        delta = min(0.0, max(-1.0, -0.5 - (spot_price - strike) / (spot_price * 0.1)))

                    # Find or create record for this strike
                    record = next((item for item in processed_data if item["strike"] == strike), None)
                    if record is None:
                        record = {
                            'strike': strike,
                            'expiry_date': nearest_expiry,
                            'call_symbol': "",
                            'put_symbol': "",
                            'call_price': 0.0,
                            'put_price': 0.0,
                            'call_volume': 0,
                            'put_volume': 0,
                            'call_oi': 0,
                            'put_oi': 0,
                            'call_iv': 0.0,
                            'put_iv': 0.0,
                            'call_change': 0.0,
                            'put_change': 0.0,
                            'call_delta': 0.0,  # Add delta field
                            'put_delta': 0.0,   # Add delta field
                            'spot_price': spot_price,
                            'time_to_expiry': time_to_expiry
                        }
                        processed_data.append(record)

                    # Get actual volume and OI from API, don't simulate
                    volume = int(option_values.get('v', 0))
                    oi = int(option_values.get('oi', 0))

                    # Update record with quote data
                    if is_call:
                        record['call_symbol'] = symbol_name
                        record['call_price'] = ltp
                        record['call_volume'] = volume
                        record['call_oi'] = oi
                        record['call_iv'] = float(option_values.get('iv', 0.2))  # Default IV of 20%
                        record['call_change'] = change
                        record['call_delta'] = delta  # Add delta value
                    else:
                        record['put_symbol'] = symbol_name
                        record['put_price'] = ltp
                        record['put_volume'] = volume
                        record['put_oi'] = oi
                        record['put_iv'] = float(option_values.get('iv', 0.2))  # Default IV of 20%
                        record['put_change'] = change
                        record['put_delta'] = delta  # Add delta value

                    logger.info(f"Processed {opt_type} option: Strike={strike} - "
                              f"LTP: {ltp:.2f}, "
                              f"Volume: {volume}, "
                              f"OI: {oi}")

            # Sort by strike price
            processed_data.sort(key=lambda x: x['strike'])
            logger.info(f"Successfully processed {len(processed_data)} option strikes")
            return processed_data

        except Exception as e:
            logger.error(f"Error fetching option chain: {str(e)}")
            return []

    def _is_weekly_expiry(self, expiry_date: str) -> bool:
        """
        Check if an expiry date is a weekly expiry.

        Args:
            expiry_date: Expiry date string from Fyers API

        Returns:
            bool: True if weekly expiry, False otherwise
        """
        try:
            from datetime import datetime
            expiry = datetime.strptime(expiry_date, "%Y-%m-%d")
            # If it's a Thursday and not the last Thursday of the month
            return expiry.weekday() == 3 and expiry.day < 22
        except Exception:
            return False

    def _format_symbol(self, symbol: str) -> str:
        """
        Format symbol for Fyers API.

        Args:
            symbol: Trading symbol

        Returns:
            str: Formatted symbol
        """
        # Add symbol formatting logic based on Fyers requirements
        if symbol.upper() == "NIFTY":
            return "NSE:NIFTY50-INDEX"
        elif symbol.upper() == "BANKNIFTY":
            return "NSE:NIFTYBANK-INDEX"  # Updated to correct format
        elif symbol.upper() == "FINNIFTY":
            return "NSE:FINNIFTY-INDEX"
        else:
            return f"NSE:{symbol.upper()}-EQ"

    def subscribe_websocket(self, symbols: List[str], callback):
        """
        Subscribe to real-time data via websocket.

        Args:
            symbols: List of symbols to subscribe to
            callback: Callback function for data updates
        """
        try:
            if not self.fyers:
                raise Exception("Not logged in to Fyers API")

            # Format symbols
            formatted_symbols = [self._format_symbol(s) for s in symbols]

            # Initialize websocket
            self.ws = FyersDataSocket(
                access_token=self.access_token,
                log_path="logs",
                on_message=callback,
                on_error=lambda msg: logger.error(f"WebSocket error: {msg}"),
                on_close=lambda: logger.info("WebSocket connection closed")
            )

            # Connect and subscribe
            self.ws.connect()
            self.ws.subscribe(formatted_symbols)

        except Exception as e:
            logger.error(f"Error in websocket subscription: {str(e)}")

    def close(self):
        """Close Fyers API connection."""
        try:
            if self.ws:
                self.ws.close()
            logger.info("Closed Fyers API connection")
        except Exception as e:
            logger.error(f"Error closing Fyers connection: {str(e)}")

    def get_weekly_pivot_ohlc_data(self, symbol: str = "NIFTY", use_cache: bool = True) -> Dict[str, Any]:
        """
        Get the OHLC data for the entire previous week (not just the last trading day)
        to calculate weekly pivot points.

        Uses caching to avoid redundant API calls for the same symbol within a short time period.

        Args:
            symbol: str - The symbol to fetch data for (default: "NIFTY")
            use_cache: bool - Whether to use cached data if available (default: True)

        Returns:
            Dict[str, Any] - A dictionary containing OHLC values from the previous week
        """
        # Ensure we have class-level cache dictionaries
        if not hasattr(self, '_ohlc_cache'):
            self._ohlc_cache = {}
            self._cache_expiry = {}

        # Cache duration in seconds (15 minutes)
        CACHE_DURATION = 900

        # Fetch fresh data if needed

        # Use a different cache key for weekly data to avoid conflicts with daily data
        cache_key = f"weekly_{symbol}"
        current_time = datetime.now()

        # Check if we have cached data that's still valid
        if use_cache and cache_key in self._ohlc_cache and cache_key in self._cache_expiry:
            if (current_time - self._cache_expiry[cache_key]).total_seconds() < CACHE_DURATION:
                logger.debug(f"Using cached weekly OHLC data for {symbol}")
                return self._ohlc_cache[cache_key]

        # Fetch fresh data
        ohlc_data = self._fetch_weekly_pivot_ohlc_data(symbol)

        # Cache the data if it's not default data (all zeros)
        is_default = all(v == 0.0 for k, v in ohlc_data.items() if k != 'date')
        if not is_default:
            self._ohlc_cache[cache_key] = ohlc_data
            self._cache_expiry[cache_key] = current_time

        return ohlc_data

    def _fetch_weekly_pivot_ohlc_data(self, symbol: str = "NIFTY") -> Dict[str, Any]:
        """
        Internal function to fetch OHLC data for the entire previous week without caching.

        This function fetches daily data for the previous week and aggregates it into weekly OHLC values:
        - Open: First day's open
        - High: Highest high of the week
        - Low: Lowest low of the week
        - Close: Last day's close
        - Volume: Sum of all daily volumes

        Args:
            symbol: str - The symbol to fetch data for (default: "NIFTY" for NIFTY 50)

        Returns:
            Dict[str, Any] - A dictionary containing OHLC values from the previous week
        """
        try:
            # Get current date for calculations
            today_utc = datetime.now(pytz.UTC).date()

            # Calculate appropriate date range for weekly data
            # Find the previous Friday (or last trading day of previous week)
            current_weekday = today_utc.weekday()  # 0=Monday, 6=Sunday
            days_to_last_friday = (current_weekday + 3) % 7
            if days_to_last_friday == 0:
                days_to_last_friday = 7  # If today is Friday, go back to previous Friday

            # Last Friday's date
            last_friday = today_utc - timedelta(days=days_to_last_friday)

            # Start date is the Monday of that week (or 4 days before Friday)
            start_date = last_friday - timedelta(days=4)
            end_date = last_friday

            logger.info(f"Current date: {today_utc.strftime('%Y-%m-%d')}")

            # Log the date range
            logger.info(f"Fetching data for previous week for {symbol}...")
            logger.info(f"Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

            # Format symbol for Fyers
            fyers_symbol = self._format_symbol(symbol)

            # Set up parameters for fetching daily data
            data = {
                "symbol": fyers_symbol,
                "resolution": "1D",  # Daily candles
                "date_format": "1",
                "range_from": start_date.strftime("%Y-%m-%d"),
                "range_to": end_date.strftime("%Y-%m-%d"),
                "cont_flag": "1"
            }

            response = self.fyers.history(data)
            if response["code"] != 200:
                logger.warning(f"No data returned for {symbol}")
                return {
                    'open': 0.0, 'high': 0.0, 'low': 0.0, 'close': 0.0,
                    'volume': 0.0, 'date': start_date.strftime('%Y-%m-%d')
                }

            # Convert to DataFrame and process data
            df = pd.DataFrame(response["candles"],
                          columns=["timestamp", "open", "high", "low", "close", "volume"])
            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s")
            df.set_index("timestamp", inplace=True)

            if df.empty:
                logger.warning(f"No data available for {symbol}")
                return {
                    'open': 0.0, 'high': 0.0, 'low': 0.0, 'close': 0.0,
                    'volume': 0.0, 'date': start_date.strftime('%Y-%m-%d')
                }

            # Get weekly aggregated data
            weekly_ohlc = {
                'open': float(df.iloc[0]['open']),    # First day's open
                'high': float(df['high'].max()),      # Week's highest
                'low': float(df['low'].min()),        # Week's lowest
                'close': float(df.iloc[-1]['close']), # Last day's close
                'volume': float(df['volume'].sum()),   # Week's total volume
                'date': df.index[0].strftime('%Y-%m-%d')  # First day of week
            }

            logger.info(f"Successfully fetched previous week's data for {symbol}")
            logger.info(f"Previous week's start date: {weekly_ohlc['date']}")
            volume_str = "Not available" if weekly_ohlc['volume'] == 0.0 else f"{int(weekly_ohlc['volume']):,}"
            logger.info(f"OHLCV: Open={weekly_ohlc['open']:.2f}, High={weekly_ohlc['high']:.2f}, "
                      f"Low={weekly_ohlc['low']:.2f}, Close={weekly_ohlc['close']:.2f}, "
                      f"Volume={volume_str}")

            # Calculate and log the pivot point for verification
            pivot = (weekly_ohlc['high'] + weekly_ohlc['low'] + weekly_ohlc['close']) / 3
            logger.info(f"Calculated pivot point: {pivot:.2f}")

            return weekly_ohlc

        except Exception as e:
            logger.error(f"Error in _fetch_weekly_pivot_ohlc_data: {e}")
            logger.warning("\nWARNING: Could not fetch previous week's data. Using default values.")
            return {
                'open': 0.0, 'high': 0.0, 'low': 0.0, 'close': 0.0,
                'volume': 0.0, 'date': datetime.now().strftime('%Y-%m-%d')
            }

    def get_spot_price(self, symbol: str) -> float:
        """
        Get real-time spot price for a symbol using Fyers quotes API.
        Handles both market hours and off-market hours.

        Args:
            symbol: Trading symbol (e.g., 'NIFTY', 'BANKNIFTY', 'NSE:NIFTY2552224250PE')

        Returns:
            float: Current spot price or 0.0 if error
        """
        try:
            if not self.fyers:
                raise Exception("Not logged in to Fyers API")

            # Format symbol for Fyers if it's not already formatted
            if not symbol.startswith("NSE:"):
                fyers_symbol = self._format_symbol(symbol)
            else:
                fyers_symbol = symbol  # Already in Fyers format

            logger.info(f"Fetching real-time price for {fyers_symbol}")

            # Use quotes API for real-time price
            response = self.fyers.quotes({"symbols": fyers_symbol})

            if response["code"] != 200:
                logger.warning(f"API returned non-200 status: {response['code']} - {response.get('message', 'No message')}")
                # Return 0.0 instead of simulating data
                logger.error(f"Failed to get real-time price for {symbol} from Fyers API")
                return 0.0

            # Extract LTP (Last Traded Price) from response
            if 'd' in response and response['d'] and len(response['d']) > 0:
                spot_data = response['d'][0]

                # Check if we have last price in the response
                if 'v' in spot_data and 'lp' in spot_data['v']:
                    spot_price = float(spot_data['v']['lp'])
                    logger.info(f"Real-time price for {fyers_symbol}: {spot_price:.2f}")
                    return spot_price

                # If no last price, check for close price
                elif 'v' in spot_data and 'c' in spot_data['v']:
                    close_price = float(spot_data['v']['c'])
                    logger.info(f"Using close price for {fyers_symbol}: {close_price:.2f} (market may be closed)")
                    return close_price

            logger.warning("No price data found in API response")
            logger.error(f"Failed to get real-time price for {symbol} from Fyers API")
            return 0.0

        except Exception as e:
            logger.error(f"Error fetching real-time price for {symbol}: {str(e)}")
            logger.error(f"Failed to get real-time price for {symbol} from Fyers API")
            return 0.0

    def _get_last_closing_price(self, symbol: str) -> float:
        """
        Get the last closing price for a symbol when real-time price is not available.
        Useful for off-market hours.

        Args:
            symbol: Trading symbol (e.g., 'NIFTY', 'BANKNIFTY')

        Returns:
            float: Last closing price or 0.0 if not available
        """
        try:
            # Format symbol for Fyers
            fyers_symbol = self._format_symbol(symbol)

            # Get today's date
            today = datetime.now().date()

            # Get yesterday's date (or last trading day)
            yesterday = today - timedelta(days=1)

            # Set up parameters for fetching daily data for the last trading day
            data = {
                "symbol": fyers_symbol,
                "resolution": "1D",  # Daily candles
                "date_format": "1",
                "range_from": yesterday.strftime("%Y-%m-%d"),
                "range_to": today.strftime("%Y-%m-%d"),
                "cont_flag": "1"
            }

            logger.info(f"Fetching last closing price for {symbol} from {yesterday} to {today}")

            # Make API call
            response = self.fyers.history(data)

            if response["code"] != 200 or "candles" not in response or not response["candles"]:
                logger.warning(f"No historical data available for {symbol}")
                return 0.0

            # Get the last candle's close price
            last_candle = response["candles"][-1]
            close_price = float(last_candle[4])  # Close price is at index 4

            logger.info(f"Last closing price for {symbol}: {close_price:.2f}")
            return close_price

        except Exception as e:
            logger.error(f"Error fetching last closing price for {symbol}: {str(e)}")
            return 0.0

    def get_multiple_spot_prices(self, symbols: List[str]) -> Dict[str, float]:
        """
        Get real-time spot prices for multiple symbols using Fyers quotes API.
        Handles both market hours and off-market hours.

        Args:
            symbols: List of trading symbols (e.g., ['NIFTY', 'NSE:NIFTY2552224250PE'])

        Returns:
            Dict[str, float]: Dictionary mapping symbols to their current spot prices
        """
        if not symbols:
            return {}

        try:
            if not self.fyers:
                raise Exception("Not logged in to Fyers API")
                
            # Check if the Fyers session is still valid
            try:
                # Make a simple API call to check session validity
                profile_response = self.fyers.get_profile()
                if isinstance(profile_response, dict) and profile_response.get("code") != 200:
                    logger.warning(f"Fyers session may have expired: {profile_response.get('message', 'Unknown error')}")
                    logger.warning("Attempting to re-login...")
                    self.login()
            except Exception as session_error:
                logger.warning(f"Error checking Fyers session: {str(session_error)}")
                logger.warning("Attempting to re-login...")
                self.login()

            # Format symbols for Fyers if needed
            fyers_symbols = []
            symbol_map = {}  # Map to keep track of original symbols

            for symbol in symbols:
                if not symbol.startswith("NSE:"):
                    fyers_symbol = self._format_symbol(symbol)
                    fyers_symbols.append(fyers_symbol)
                    symbol_map[fyers_symbol] = symbol
                else:
                    fyers_symbols.append(symbol)  # Already in Fyers format
                    symbol_map[symbol] = symbol

            logger.info(f"Fetching real-time prices for {len(fyers_symbols)} symbols")

            # Process in smaller batches to avoid API limitations
            batch_size = 25  # Reduced from 50 to see if it helps
            result = {}
            
            for i in range(0, len(fyers_symbols), batch_size):
                batch = fyers_symbols[i:i + batch_size]
                logger.info(f"Processing batch {i//batch_size + 1} of {(len(fyers_symbols) + batch_size - 1)//batch_size} ({len(batch)} symbols)")
                
                # Join symbols with comma for API call
                symbols_str = ",".join(batch)
                
                try:
                    # Log the symbols being requested
                    logger.info(f"Requesting quotes for symbols: {symbols_str}")
                    
                    # Use quotes API for real-time prices
                    response = self.fyers.quotes({"symbols": symbols_str})
                    
                    # Log the raw response for debugging
                    logger.info(f"API response code: {response.get('code') if isinstance(response, dict) else 'N/A'}")
                    logger.info(f"API response message: {response.get('message') if isinstance(response, dict) else 'N/A'}")
                    logger.info(f"API response has data: {'Yes' if isinstance(response, dict) and 'd' in response else 'No'}")
                    logger.info(f"API response data length: {len(response.get('d', [])) if isinstance(response, dict) and 'd' in response else 0}")
                    
                    if response and isinstance(response, dict) and response.get("code") == 200 and 'd' in response and response['d']:
                        # Log the first data item to see its structure
                        if response['d'] and len(response['d']) > 0:
                            logger.info(f"First data item structure: {response['d'][0]}")
                        
                        # Count how many items have 'n' and 'v' fields
                        valid_items = 0
                        for data in response['d']:
                            if 'n' in data and 'v' in data:
                                valid_items += 1
                        logger.info(f"Found {valid_items} items with 'n' and 'v' fields out of {len(response['d'])}")
                        
                        for data in response['d']:
                            if 'n' in data and 'v' in data:
                                symbol = data['n']
                                logger.info(f"Processing symbol: {symbol}")
                                
                                # Get volume and open interest if available
                                volume = int(data['v'].get('v', 0))
                                oi = int(data['v'].get('oi', 0))
                                
                                # Log the 'v' structure to see what fields are available
                                logger.info(f"Value structure for {symbol}: {data['v'].keys()}")
                                
                                # Check if there's an error in the response for this symbol
                                if 'code' in data['v'] and data['v'].get('code') < 0:
                                    error_code = data['v'].get('code')
                                    error_msg = data['v'].get('errmsg', 'Unknown error')
                                    logger.warning(f"Error for symbol {symbol}: {error_code} - {error_msg}")
                                    continue  # Skip this symbol
                                
                                # Try to get last price first
                                if 'lp' in data['v']:
                                    price = float(data['v']['lp'])
                                    # Store the price using the original symbol from the request
                                    # This ensures we're using the exact same symbol format as the caller expects
                                    if symbol in symbol_map:
                                        original_symbol = symbol_map[symbol]
                                        result[original_symbol] = price
                                        logger.info(f"Real-time price for {original_symbol} (API: {symbol}): {price:.2f}, Volume: {volume}, OI: {oi}")
                                    else:
                                        # If not in map, use as-is (shouldn't happen but just in case)
                                        result[symbol] = price
                                        logger.info(f"Real-time price for {symbol}: {price:.2f}, Volume: {volume}, OI: {oi}")
                                # Fall back to close price if last price not available
                                elif 'c' in data['v']:
                                    price = float(data['v']['c'])
                                    # Store using original symbol
                                    if symbol in symbol_map:
                                        original_symbol = symbol_map[symbol]
                                        result[original_symbol] = price
                                        logger.info(f"Using close price for {original_symbol} (API: {symbol}): {price:.2f} (market may be closed)")
                                    else:
                                        result[symbol] = price
                                        logger.info(f"Using close price for {symbol}: {price:.2f} (market may be closed)")
                                else:
                                    logger.warning(f"No price data found for {symbol}. Available fields: {data['v'].keys()}")
                    else:
                        # Check for specific error conditions
                        if isinstance(response, dict):
                            error_code = response.get("code")
                            error_message = response.get("message", "No error message provided")
                            
                            if error_code == 400:
                                logger.error(f"Bad request error: {error_message}")
                                logger.error("This might indicate invalid symbol format or other request issues")
                            elif error_code == 401:
                                logger.error(f"Authentication error: {error_message}")
                                logger.error("Please check your Fyers API credentials and login status")
                            elif error_code == 429:
                                logger.error(f"Rate limit exceeded: {error_message}")
                                logger.error("Too many requests in a short time period")
                            elif error_code == 500:
                                logger.error(f"Fyers API server error: {error_message}")
                            else:
                                logger.error(f"API returned non-200 status: {error_code} - {error_message}")
                        else:
                            logger.error(f"API returned unexpected response format: {response}")
                        
                        logger.error(f"Batch contained {len(batch)} symbols")
                        
                        # Check for missing symbols
                        if result:
                            missing_symbols = [s for s in batch if s not in result]
                            if missing_symbols:
                                logger.warning(f"Missing data for {len(missing_symbols)} symbols out of {len(batch)}")
                                for sym in missing_symbols[:min(10, len(missing_symbols))]:
                                    logger.warning(f"Missing data for symbol: {sym}")
                                if len(missing_symbols) > 10:
                                    logger.warning(f"... and {len(missing_symbols) - 10} more symbols")
                
                except Exception as batch_error:
                    logger.error(f"Error processing batch {i//batch_size + 1}: {str(batch_error)}")
                    continue
                
                # Add a small delay between batches to avoid rate limiting
                if i + batch_size < len(fyers_symbols):
                    logger.info("Adding delay between batches to avoid rate limiting...")
                    time.sleep(1)  # 1 second delay

            # Check if we got all the symbols
            missing_symbols = set(fyers_symbols) - set(result.keys())

            # Log detailed information about results
            logger.info(f"Successfully fetched prices for {len(result)} symbols out of {len(fyers_symbols)}")
            
            # Log a few examples of successful symbols
            if result:
                logger.info("Examples of successful symbols:")
                for i, (sym, price) in enumerate(list(result.items())[:5]):
                    logger.info(f"  {i+1}. {sym}: {price}")
            
            # Log missing symbols but don't try to simulate data
            if missing_symbols:
                logger.warning(f"Could not fetch real-time prices for {len(missing_symbols)} symbols.")
                logger.warning("This might be due to:")
                logger.warning("1. Symbol format issues")
                logger.warning("2. Symbols not being available for trading")
                logger.warning("3. Market being closed for these instruments")
                logger.warning("4. API limitations or restrictions")
                
                logger.warning("Missing symbols:")
                for i, missing in enumerate(list(missing_symbols)[:min(len(missing_symbols), 20)]):
                    logger.warning(f"  {i+1}. {missing}")
                
                if len(missing_symbols) > 20:
                    logger.warning(f"  ... and {len(missing_symbols) - 20} more")

            return result

        except Exception as e:
            logger.error(f"Error fetching multiple spot prices: {str(e)}")
            logger.error("Failed to get real-time prices from Fyers API")
            
            # Return empty result instead of simulating data
            return {}

    def _calculate_time_to_expiry(self, expiry_date) -> float:
        """Calculate time to expiry in years."""
        if isinstance(expiry_date, str):
            expiry = datetime.strptime(expiry_date, "%Y-%m-%d")
        else:
            expiry = datetime.combine(expiry_date, datetime.min.time())

        now = datetime.now()
        days_to_expiry = (expiry - now).days + 1  # Add 1 to include today
        return max(days_to_expiry / 365.0, 0.0001)  # Minimum value to avoid division by zero
