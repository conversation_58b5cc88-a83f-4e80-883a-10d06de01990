"""
Consolidated Shortlist Engine for the Pivot Point Strategy Application.

This module consolidates filtered options from delta and pivot premium filters:
1. Combines delta-filtered and pivot-premium-filtered options
2. Applies final ranking and scoring algorithms
3. Creates trading-ready shortlists
4. Provides comprehensive option analysis
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)


class ShortlistEngine:
    """
    Engine to consolidate and rank filtered options for trading.
    """
    
    def __init__(self, config: dict):
        """
        Initialize the shortlist engine.
        
        Args:
            config: Configuration dictionary for shortlist engine settings
        """
        self.config = config
        self.max_final_options = config.get('max_final_options', 10)
        self.scoring_weights = config.get('scoring_weights', {
            'delta_score': 0.3,
            'liquidity_score': 0.3,
            'pivot_proximity_score': 0.2,
            'volume_score': 0.2
        })
        self.min_combined_score = config.get('min_combined_score', 0.5)
        self.prioritize_atm_options = config.get('prioritize_atm_options', True)
        
        logger.info(f"ShortlistEngine initialized with max_final_options: {self.max_final_options}")
    
    def consolidate_filtered_options(self, delta_filtered: Dict[str, List[Dict[str, Any]]], 
                                   pivot_filtered: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Consolidate options from delta and pivot premium filters.
        
        Args:
            delta_filtered: Options filtered by delta criteria
            pivot_filtered: Options filtered by pivot premium criteria
            
        Returns:
            Dict: Consolidated options with combined data
        """
        try:
            logger.info("Consolidating delta and pivot filtered options")
            
            # Get options from both filters
            delta_calls = delta_filtered.get('calls', [])
            delta_puts = delta_filtered.get('puts', [])
            pivot_calls = pivot_filtered.get('calls', [])
            pivot_puts = pivot_filtered.get('puts', [])
            
            # Consolidate calls
            consolidated_calls = self._merge_option_lists(delta_calls, pivot_calls, 'call')
            
            # Consolidate puts
            consolidated_puts = self._merge_option_lists(delta_puts, pivot_puts, 'put')
            
            logger.info(f"Consolidated {len(consolidated_calls)} calls and {len(consolidated_puts)} puts")
            
            return {
                'calls': consolidated_calls,
                'puts': consolidated_puts
            }
            
        except Exception as e:
            logger.error(f"Error consolidating filtered options: {e}")
            return {'calls': [], 'puts': []}
    
    def _merge_option_lists(self, delta_options: List[Dict[str, Any]], 
                           pivot_options: List[Dict[str, Any]], 
                           option_type: str) -> List[Dict[str, Any]]:
        """
        Merge two lists of options, combining data for matching strikes.
        
        Args:
            delta_options: Options from delta filter
            pivot_options: Options from pivot filter
            option_type: 'call' or 'put'
            
        Returns:
            List: Merged options with combined data
        """
        try:
            merged_options = {}
            
            # Process delta filtered options
            for option in delta_options:
                strike = option.get('strike')
                if strike:
                    merged_options[strike] = option.copy()
                    merged_options[strike]['passed_delta_filter'] = True
                    merged_options[strike]['passed_pivot_filter'] = False
            
            # Process pivot filtered options
            for option in pivot_options:
                strike = option.get('strike')
                if strike:
                    if strike in merged_options:
                        # Merge data for existing strike
                        merged_options[strike]['passed_pivot_filter'] = True
                        # Add pivot-specific data
                        for key, value in option.items():
                            if key.startswith('pivot_') or key.startswith('closest_pivot'):
                                merged_options[strike][key] = value
                    else:
                        # New strike from pivot filter
                        merged_options[strike] = option.copy()
                        merged_options[strike]['passed_delta_filter'] = False
                        merged_options[strike]['passed_pivot_filter'] = True
            
            # Convert back to list and add filter status
            result = []
            for strike, option in merged_options.items():
                option['passed_both_filters'] = (
                    option.get('passed_delta_filter', False) and 
                    option.get('passed_pivot_filter', False)
                )
                result.append(option)
            
            return result
            
        except Exception as e:
            logger.error(f"Error merging {option_type} option lists: {e}")
            return []
    
    def calculate_comprehensive_scores(self, consolidated_options: Dict[str, List[Dict[str, Any]]], 
                                     current_price: float) -> Dict[str, List[Dict[str, Any]]]:
        """
        Calculate comprehensive scores for all consolidated options.
        
        Args:
            consolidated_options: Consolidated options from both filters
            current_price: Current spot price
            
        Returns:
            Dict: Options with calculated scores
        """
        try:
            scored_calls = []
            scored_puts = []
            
            # Score call options
            for option in consolidated_options.get('calls', []):
                scored_option = self._calculate_option_score(option, current_price, 'call')
                scored_calls.append(scored_option)
            
            # Score put options
            for option in consolidated_options.get('puts', []):
                scored_option = self._calculate_option_score(option, current_price, 'put')
                scored_puts.append(scored_option)
            
            logger.info(f"Calculated scores for {len(scored_calls)} calls and {len(scored_puts)} puts")
            
            return {
                'calls': scored_calls,
                'puts': scored_puts
            }
            
        except Exception as e:
            logger.error(f"Error calculating comprehensive scores: {e}")
            return consolidated_options
    
    def _calculate_option_score(self, option: Dict[str, Any], current_price: float, option_type: str) -> Dict[str, Any]:
        """
        Calculate comprehensive score for a single option.
        
        Args:
            option: Option data dictionary
            current_price: Current spot price
            option_type: 'call' or 'put'
            
        Returns:
            Dict: Option with calculated scores
        """
        try:
            scored_option = option.copy()
            strike = option.get('strike', 0)
            
            # Calculate individual scores
            delta_score = self._calculate_delta_score(option, option_type)
            liquidity_score = self._calculate_liquidity_score_comprehensive(option, option_type)
            pivot_proximity_score = self._calculate_pivot_proximity_score(option)
            volume_score = self._calculate_volume_score(option, option_type)
            atm_score = self._calculate_atm_score(strike, current_price)
            
            # Calculate weighted combined score
            combined_score = (
                delta_score * self.scoring_weights.get('delta_score', 0.3) +
                liquidity_score * self.scoring_weights.get('liquidity_score', 0.3) +
                pivot_proximity_score * self.scoring_weights.get('pivot_proximity_score', 0.2) +
                volume_score * self.scoring_weights.get('volume_score', 0.2)
            )
            
            # Apply ATM bonus if enabled
            if self.prioritize_atm_options:
                combined_score += atm_score * 0.1
            
            # Store all scores
            scored_option['scores'] = {
                'delta_score': round(delta_score, 3),
                'liquidity_score': round(liquidity_score, 3),
                'pivot_proximity_score': round(pivot_proximity_score, 3),
                'volume_score': round(volume_score, 3),
                'atm_score': round(atm_score, 3),
                'combined_score': round(combined_score, 3)
            }
            
            return scored_option
            
        except Exception as e:
            logger.error(f"Error calculating score for {option_type} option: {e}")
            option['scores'] = {'combined_score': 0.0}
            return option
    
    def _calculate_delta_score(self, option: Dict[str, Any], option_type: str) -> float:
        """Calculate delta-based score (0-1)."""
        try:
            delta = option.get('calculated_delta', 0)
            if option_type == 'put':
                delta = abs(delta)
            
            # Score based on delta range (0.3-0.65 is ideal)
            if 0.3 <= delta <= 0.65:
                # Higher score for deltas closer to 0.5
                distance_from_ideal = abs(delta - 0.5)
                score = 1.0 - (distance_from_ideal / 0.2)  # 0.2 is max distance from 0.5 in range
                return max(0.0, min(1.0, score))
            else:
                return 0.0
                
        except Exception:
            return 0.0
    
    def _calculate_liquidity_score_comprehensive(self, option: Dict[str, Any], option_type: str) -> float:
        """Calculate comprehensive liquidity score (0-1)."""
        try:
            volume_key = f"{option_type}_volume"
            oi_key = f"{option_type}_oi"
            ltp_key = f"{option_type}_ltp"
            
            volume = option.get(volume_key, 0)
            oi = option.get(oi_key, 0)
            ltp = option.get(ltp_key, 0)
            
            # Normalize each component
            volume_norm = min(volume / 10000, 1.0)  # Max at 10k volume
            oi_norm = min(oi / 50000, 1.0)  # Max at 50k OI
            ltp_norm = min(ltp / 100, 1.0)  # Max at 100 price
            
            # Weighted average
            liquidity_score = volume_norm * 0.4 + oi_norm * 0.4 + ltp_norm * 0.2
            
            return liquidity_score
            
        except Exception:
            return 0.0
    
    def _calculate_pivot_proximity_score(self, option: Dict[str, Any]) -> float:
        """Calculate pivot proximity score (0-1)."""
        try:
            distance_pct = option.get('closest_pivot_distance_pct', float('inf'))
            
            if distance_pct == float('inf'):
                return 0.0
            
            # Higher score for closer proximity to pivot levels
            # Max score for 0% distance, decreasing to 0 at 10% distance
            if distance_pct <= 10.0:
                score = 1.0 - (distance_pct / 10.0)
                return max(0.0, score)
            else:
                return 0.0
                
        except Exception:
            return 0.0
    
    def _calculate_volume_score(self, option: Dict[str, Any], option_type: str) -> float:
        """Calculate volume-based score (0-1)."""
        try:
            volume_key = f"{option_type}_volume"
            volume = option.get(volume_key, 0)
            
            # Logarithmic scaling for volume score
            if volume > 0:
                score = min(np.log10(volume) / 5.0, 1.0)  # Max score at 100k volume
                return max(0.0, score)
            else:
                return 0.0
                
        except Exception:
            return 0.0
    
    def _calculate_atm_score(self, strike: float, current_price: float) -> float:
        """Calculate at-the-money score (0-1)."""
        try:
            if strike <= 0 or current_price <= 0:
                return 0.0
            
            distance_pct = abs(strike - current_price) / current_price * 100
            
            # Higher score for strikes closer to current price
            if distance_pct <= 5.0:  # Within 5% of current price
                score = 1.0 - (distance_pct / 5.0)
                return max(0.0, score)
            else:
                return 0.0
                
        except Exception:
            return 0.0
    
    def create_final_shortlist(self, scored_options: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Create final trading shortlist from scored options.
        
        Args:
            scored_options: Options with calculated scores
            
        Returns:
            Dict: Final shortlist for trading
        """
        try:
            # Filter options by minimum score
            qualified_calls = [
                opt for opt in scored_options.get('calls', [])
                if opt.get('scores', {}).get('combined_score', 0) >= self.min_combined_score
            ]
            
            qualified_puts = [
                opt for opt in scored_options.get('puts', [])
                if opt.get('scores', {}).get('combined_score', 0) >= self.min_combined_score
            ]
            
            # Sort by combined score (descending)
            qualified_calls.sort(
                key=lambda x: x.get('scores', {}).get('combined_score', 0), 
                reverse=True
            )
            qualified_puts.sort(
                key=lambda x: x.get('scores', {}).get('combined_score', 0), 
                reverse=True
            )
            
            # Limit to maximum final options
            final_calls = qualified_calls[:self.max_final_options]
            final_puts = qualified_puts[:self.max_final_options]
            
            # Add ranking information
            for i, option in enumerate(final_calls):
                option['final_rank'] = i + 1
                option['option_type'] = 'call'
            
            for i, option in enumerate(final_puts):
                option['final_rank'] = i + 1
                option['option_type'] = 'put'
            
            logger.info(f"Final shortlist created: {len(final_calls)} calls, {len(final_puts)} puts")
            
            return {
                'calls': final_calls,
                'puts': final_puts,
                'total_shortlisted': len(final_calls) + len(final_puts)
            }
            
        except Exception as e:
            logger.error(f"Error creating final shortlist: {e}")
            return {'calls': [], 'puts': [], 'total_shortlisted': 0}
    
    def get_shortlist_summary(self, final_shortlist: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        Get summary statistics for the final shortlist.
        
        Args:
            final_shortlist: Final shortlist results
            
        Returns:
            Dict: Shortlist summary
        """
        try:
            calls = final_shortlist.get('calls', [])
            puts = final_shortlist.get('puts', [])
            all_options = calls + puts
            
            if not all_options:
                return {'total_options': 0}
            
            # Calculate summary statistics
            scores = [opt.get('scores', {}).get('combined_score', 0) for opt in all_options]
            
            summary = {
                'total_options': len(all_options),
                'total_calls': len(calls),
                'total_puts': len(puts),
                'avg_combined_score': round(np.mean(scores), 3),
                'min_combined_score': round(min(scores), 3),
                'max_combined_score': round(max(scores), 3),
                'options_passed_both_filters': len([
                    opt for opt in all_options 
                    if opt.get('passed_both_filters', False)
                ]),
                'top_call': calls[0] if calls else None,
                'top_put': puts[0] if puts else None
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating shortlist summary: {e}")
            return {'total_options': 0}
    
    def update_config(self, new_config: dict):
        """
        Update engine configuration.
        
        Args:
            new_config: New configuration dictionary
        """
        try:
            self.config.update(new_config)
            self.max_final_options = self.config.get('max_final_options', self.max_final_options)
            self.min_combined_score = self.config.get('min_combined_score', self.min_combined_score)
            
            logger.info("Shortlist engine configuration updated")
            
        except Exception as e:
            logger.error(f"Error updating shortlist engine configuration: {e}")
