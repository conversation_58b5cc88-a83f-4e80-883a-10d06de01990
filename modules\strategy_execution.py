"""
Strategy Execution Engine for the Pivot Point Strategy Application.

This module handles the execution of trading strategies:
1. Executes at 9:45 AM IST with market analysis
2. Implements BUY/SELL sequence patterns
3. Manages position sizing and risk
4. Tracks trade execution and performance
"""

import logging
import asyncio
from datetime import datetime, time
from typing import Dict, List, Any, Optional, Tuple
import pytz

logger = logging.getLogger(__name__)


class StrategyExecutionEngine:
    """
    Engine for executing pivot point trading strategies.
    """
    
    def __init__(self, config: dict, fyers_client=None):
        """
        Initialize the strategy execution engine.
        
        Args:
            config: Configuration dictionary for strategy execution
            fyers_client: Fyers API client for order execution
        """
        self.config = config
        self.fyers_client = fyers_client
        
        # Execution settings
        self.execution_time = time(9, 45)  # 9:45 AM IST
        self.max_positions = config.get('max_positions', 5)
        self.position_size = config.get('position_size', 1)  # Number of lots
        self.max_risk_per_trade = config.get('max_risk_per_trade', 1000)  # Max loss per trade
        self.enable_live_trading = config.get('enable_live_trading', False)
        
        # Strategy patterns
        self.buy_patterns = config.get('buy_patterns', ['BULLISH_PIVOT', 'DELTA_CONVERGENCE'])
        self.sell_patterns = config.get('sell_patterns', ['BEARISH_PIVOT', 'DELTA_DIVERGENCE'])
        
        # Tracking
        self.active_positions = {}
        self.executed_trades = []
        self.daily_pnl = 0.0
        
        # IST timezone
        self.ist_tz = pytz.timezone('Asia/Kolkata')
        
        logger.info(f"StrategyExecutionEngine initialized. Live trading: {self.enable_live_trading}")
    
    def is_execution_time(self) -> bool:
        """
        Check if current time is the strategy execution time.
        
        Returns:
            bool: True if it's execution time
        """
        try:
            current_time = datetime.now(self.ist_tz).time()
            
            # Allow execution within 5 minutes of target time
            execution_start = time(9, 45)
            execution_end = time(9, 50)
            
            return execution_start <= current_time <= execution_end
            
        except Exception as e:
            logger.error(f"Error checking execution time: {e}")
            return False
    
    def analyze_market_conditions(self, shortlist: Dict[str, List[Dict[str, Any]]], 
                                 pivot_analysis: Dict[str, Any], 
                                 current_price: float) -> Dict[str, Any]:
        """
        Analyze current market conditions for strategy execution.
        
        Args:
            shortlist: Final shortlist of options
            pivot_analysis: Pivot point analysis
            current_price: Current spot price
            
        Returns:
            Dict: Market analysis results
        """
        try:
            analysis = {
                'timestamp': datetime.now(self.ist_tz).strftime('%Y-%m-%d %H:%M:%S'),
                'current_price': current_price,
                'market_bias': 'NEUTRAL',
                'signal_strength': 0.0,
                'recommended_action': 'HOLD',
                'target_options': []
            }
            
            # Analyze pivot levels
            pivot_levels = pivot_analysis.get('current_pivots', {})
            if pivot_levels:
                analysis['pivot_analysis'] = self._analyze_pivot_position(
                    current_price, pivot_levels
                )
            
            # Analyze shortlisted options
            calls = shortlist.get('calls', [])
            puts = shortlist.get('puts', [])
            
            if calls or puts:
                analysis['option_analysis'] = self._analyze_option_signals(calls, puts)
            
            # Determine overall market bias
            analysis['market_bias'] = self._determine_market_bias(analysis)
            
            # Calculate signal strength
            analysis['signal_strength'] = self._calculate_signal_strength(analysis)
            
            # Generate trading recommendations
            analysis['recommended_action'] = self._generate_trading_recommendation(analysis)
            
            # Select target options for execution
            analysis['target_options'] = self._select_target_options(shortlist, analysis)
            
            logger.info(f"Market analysis completed. Bias: {analysis['market_bias']}, "
                       f"Signal: {analysis['signal_strength']:.2f}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing market conditions: {e}")
            return {'market_bias': 'NEUTRAL', 'signal_strength': 0.0}
    
    def _analyze_pivot_position(self, current_price: float, pivot_levels: Dict[str, float]) -> Dict[str, Any]:
        """Analyze current price position relative to pivot levels."""
        try:
            pivot = pivot_levels.get('Pivot', 0)
            if pivot == 0:
                return {'position': 'UNKNOWN'}
            
            # Determine position relative to pivot
            if current_price > pivot:
                position = 'ABOVE_PIVOT'
                resistance_levels = [
                    ('R1', pivot_levels.get('R1', 0)),
                    ('R2', pivot_levels.get('R2', 0)),
                    ('R3', pivot_levels.get('R3', 0))
                ]
                next_level = min([r for r in resistance_levels if r[1] > current_price], 
                               key=lambda x: x[1], default=('R3', pivot_levels.get('R3', 0)))
            else:
                position = 'BELOW_PIVOT'
                support_levels = [
                    ('S1', pivot_levels.get('S1', 0)),
                    ('S2', pivot_levels.get('S2', 0)),
                    ('S3', pivot_levels.get('S3', 0))
                ]
                next_level = max([s for s in support_levels if s[1] < current_price], 
                               key=lambda x: x[1], default=('S3', pivot_levels.get('S3', 0)))
            
            distance_to_pivot = abs(current_price - pivot)
            distance_percentage = (distance_to_pivot / current_price) * 100
            
            return {
                'position': position,
                'distance_to_pivot': round(distance_to_pivot, 2),
                'distance_percentage': round(distance_percentage, 2),
                'next_level': next_level[0],
                'next_level_value': next_level[1],
                'distance_to_next_level': round(abs(current_price - next_level[1]), 2)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing pivot position: {e}")
            return {'position': 'UNKNOWN'}
    
    def _analyze_option_signals(self, calls: List[Dict[str, Any]], puts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze signals from shortlisted options."""
        try:
            analysis = {
                'call_strength': 0.0,
                'put_strength': 0.0,
                'dominant_signal': 'NEUTRAL'
            }
            
            # Analyze call options
            if calls:
                call_scores = [opt.get('scores', {}).get('combined_score', 0) for opt in calls]
                analysis['call_strength'] = sum(call_scores) / len(call_scores)
                analysis['top_call_score'] = max(call_scores)
            
            # Analyze put options
            if puts:
                put_scores = [opt.get('scores', {}).get('combined_score', 0) for opt in puts]
                analysis['put_strength'] = sum(put_scores) / len(put_scores)
                analysis['top_put_score'] = max(put_scores)
            
            # Determine dominant signal
            if analysis['call_strength'] > analysis['put_strength'] * 1.2:
                analysis['dominant_signal'] = 'BULLISH'
            elif analysis['put_strength'] > analysis['call_strength'] * 1.2:
                analysis['dominant_signal'] = 'BEARISH'
            else:
                analysis['dominant_signal'] = 'NEUTRAL'
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing option signals: {e}")
            return {'dominant_signal': 'NEUTRAL'}
    
    def _determine_market_bias(self, analysis: Dict[str, Any]) -> str:
        """Determine overall market bias from analysis."""
        try:
            pivot_analysis = analysis.get('pivot_analysis', {})
            option_analysis = analysis.get('option_analysis', {})
            
            pivot_position = pivot_analysis.get('position', 'UNKNOWN')
            option_signal = option_analysis.get('dominant_signal', 'NEUTRAL')
            
            # Combine signals
            if pivot_position == 'ABOVE_PIVOT' and option_signal == 'BULLISH':
                return 'BULLISH'
            elif pivot_position == 'BELOW_PIVOT' and option_signal == 'BEARISH':
                return 'BEARISH'
            elif option_signal in ['BULLISH', 'BEARISH']:
                return option_signal
            else:
                return 'NEUTRAL'
                
        except Exception as e:
            logger.error(f"Error determining market bias: {e}")
            return 'NEUTRAL'
    
    def _calculate_signal_strength(self, analysis: Dict[str, Any]) -> float:
        """Calculate overall signal strength (0-1)."""
        try:
            option_analysis = analysis.get('option_analysis', {})
            pivot_analysis = analysis.get('pivot_analysis', {})
            
            # Base strength from option scores
            call_strength = option_analysis.get('call_strength', 0)
            put_strength = option_analysis.get('put_strength', 0)
            base_strength = max(call_strength, put_strength)
            
            # Boost from pivot proximity
            distance_pct = pivot_analysis.get('distance_percentage', 100)
            proximity_boost = max(0, 1 - (distance_pct / 5))  # Boost if within 5%
            
            # Combined strength
            signal_strength = min(1.0, base_strength + proximity_boost * 0.2)
            
            return round(signal_strength, 3)
            
        except Exception as e:
            logger.error(f"Error calculating signal strength: {e}")
            return 0.0
    
    def _generate_trading_recommendation(self, analysis: Dict[str, Any]) -> str:
        """Generate trading recommendation based on analysis."""
        try:
            market_bias = analysis.get('market_bias', 'NEUTRAL')
            signal_strength = analysis.get('signal_strength', 0)
            
            # Minimum signal strength threshold
            if signal_strength < 0.6:
                return 'HOLD'
            
            # Generate recommendation based on bias and strength
            if market_bias == 'BULLISH' and signal_strength >= 0.7:
                return 'BUY_CALLS'
            elif market_bias == 'BEARISH' and signal_strength >= 0.7:
                return 'BUY_PUTS'
            elif signal_strength >= 0.8:
                return f'BUY_{market_bias}'
            else:
                return 'HOLD'
                
        except Exception as e:
            logger.error(f"Error generating trading recommendation: {e}")
            return 'HOLD'
    
    def _select_target_options(self, shortlist: Dict[str, List[Dict[str, Any]]], 
                              analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Select specific options for execution."""
        try:
            recommendation = analysis.get('recommended_action', 'HOLD')
            target_options = []
            
            if recommendation == 'HOLD':
                return target_options
            
            calls = shortlist.get('calls', [])
            puts = shortlist.get('puts', [])
            
            # Select based on recommendation
            if 'CALLS' in recommendation and calls:
                # Select top call options
                target_options.extend(calls[:min(2, len(calls))])
            
            if 'PUTS' in recommendation and puts:
                # Select top put options
                target_options.extend(puts[:min(2, len(puts))])
            
            if recommendation.startswith('BUY_BULLISH'):
                target_options.extend(calls[:1])
            elif recommendation.startswith('BUY_BEARISH'):
                target_options.extend(puts[:1])
            
            # Limit total positions
            target_options = target_options[:self.max_positions]
            
            logger.info(f"Selected {len(target_options)} target options for execution")
            
            return target_options
            
        except Exception as e:
            logger.error(f"Error selecting target options: {e}")
            return []
    
    async def execute_strategy(self, market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the trading strategy based on market analysis.
        
        Args:
            market_analysis: Market analysis results
            
        Returns:
            Dict: Execution results
        """
        try:
            execution_results = {
                'timestamp': datetime.now(self.ist_tz).strftime('%Y-%m-%d %H:%M:%S'),
                'executed_trades': [],
                'failed_trades': [],
                'total_executed': 0,
                'execution_status': 'COMPLETED'
            }
            
            recommendation = market_analysis.get('recommended_action', 'HOLD')
            target_options = market_analysis.get('target_options', [])
            
            if recommendation == 'HOLD' or not target_options:
                logger.info("No trading action recommended")
                execution_results['execution_status'] = 'NO_ACTION'
                return execution_results
            
            logger.info(f"Executing strategy: {recommendation} with {len(target_options)} options")
            
            # Execute trades for each target option
            for option in target_options:
                if len(self.active_positions) >= self.max_positions:
                    logger.warning("Maximum positions reached, skipping remaining trades")
                    break
                
                trade_result = await self._execute_single_trade(option, recommendation)
                
                if trade_result.get('success', False):
                    execution_results['executed_trades'].append(trade_result)
                    execution_results['total_executed'] += 1
                else:
                    execution_results['failed_trades'].append(trade_result)
            
            logger.info(f"Strategy execution completed. "
                       f"Executed: {execution_results['total_executed']}, "
                       f"Failed: {len(execution_results['failed_trades'])}")
            
            return execution_results
            
        except Exception as e:
            logger.error(f"Error executing strategy: {e}")
            return {'execution_status': 'ERROR', 'error': str(e)}
    
    async def _execute_single_trade(self, option: Dict[str, Any], action: str) -> Dict[str, Any]:
        """Execute a single trade for an option."""
        try:
            trade_result = {
                'option_symbol': option.get('call_symbol') or option.get('put_symbol'),
                'strike': option.get('strike'),
                'action': action,
                'success': False,
                'timestamp': datetime.now(self.ist_tz).strftime('%Y-%m-%d %H:%M:%S')
            }
            
            if not self.enable_live_trading:
                # Simulation mode
                trade_result.update({
                    'success': True,
                    'order_id': f"SIM_{datetime.now().strftime('%H%M%S')}",
                    'quantity': self.position_size,
                    'price': option.get('call_ltp') or option.get('put_ltp', 0),
                    'mode': 'SIMULATION'
                })
                
                # Add to active positions
                self.active_positions[trade_result['option_symbol']] = trade_result
                
                logger.info(f"Simulated trade executed: {trade_result['option_symbol']}")
            else:
                # Live trading mode (placeholder for actual implementation)
                logger.warning("Live trading not implemented yet")
                trade_result['error'] = 'Live trading not implemented'
            
            return trade_result
            
        except Exception as e:
            logger.error(f"Error executing single trade: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_active_positions(self) -> Dict[str, Any]:
        """Get current active positions."""
        return {
            'positions': list(self.active_positions.values()),
            'total_positions': len(self.active_positions),
            'daily_pnl': self.daily_pnl
        }
    
    def update_config(self, new_config: dict):
        """Update execution engine configuration."""
        try:
            self.config.update(new_config)
            self.max_positions = self.config.get('max_positions', self.max_positions)
            self.position_size = self.config.get('position_size', self.position_size)
            self.enable_live_trading = self.config.get('enable_live_trading', self.enable_live_trading)
            
            logger.info("Strategy execution configuration updated")
            
        except Exception as e:
            logger.error(f"Error updating execution configuration: {e}")
