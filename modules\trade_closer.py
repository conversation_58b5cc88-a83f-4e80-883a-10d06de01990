"""
Trade Closer Module for the Pivot Point Strategy Application.

This module handles the closing of positions:
1. Closes positions at 3:06 PM IST
2. Calculates P&L for each trade
3. Logs trade performance
4. Generates daily trade reports
"""

import logging
import asyncio
from datetime import datetime, time
from typing import Dict, List, Any, Optional, Tuple
import pytz

logger = logging.getLogger(__name__)


class TradeCloser:
    """
    Module for closing trades and calculating P&L.
    """
    
    def __init__(self, config: dict, fyers_client=None):
        """
        Initialize the trade closer.
        
        Args:
            config: Configuration dictionary for trade closer settings
            fyers_client: Fyers API client for order execution
        """
        self.config = config
        self.fyers_client = fyers_client
        
        # Closing settings
        self.closing_time = time(15, 6)  # 3:06 PM IST
        self.force_close_time = time(15, 25)  # 3:25 PM IST (before market close)
        self.enable_live_trading = config.get('enable_live_trading', False)
        self.min_profit_threshold = config.get('min_profit_threshold', 50)  # Minimum profit to close
        self.max_loss_threshold = config.get('max_loss_threshold', -500)  # Maximum loss before force close
        
        # Tracking
        self.closed_trades = []
        self.daily_pnl = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        
        # IST timezone
        self.ist_tz = pytz.timezone('Asia/Kolkata')
        
        logger.info(f"TradeCloser initialized. Live trading: {self.enable_live_trading}")
    
    def is_closing_time(self) -> bool:
        """
        Check if current time is the trade closing time.
        
        Returns:
            bool: True if it's closing time
        """
        try:
            current_time = datetime.now(self.ist_tz).time()
            
            # Allow closing within 10 minutes of target time
            closing_start = time(15, 6)
            closing_end = time(15, 16)
            
            return closing_start <= current_time <= closing_end
            
        except Exception as e:
            logger.error(f"Error checking closing time: {e}")
            return False
    
    def is_force_close_time(self) -> bool:
        """
        Check if current time requires force closing all positions.
        
        Returns:
            bool: True if force close is required
        """
        try:
            current_time = datetime.now(self.ist_tz).time()
            return current_time >= self.force_close_time
            
        except Exception as e:
            logger.error(f"Error checking force close time: {e}")
            return False
    
    async def close_all_positions(self, active_positions: Dict[str, Any]) -> Dict[str, Any]:
        """
        Close all active positions and calculate P&L.
        
        Args:
            active_positions: Dictionary of active positions
            
        Returns:
            Dict: Closing results with P&L information
        """
        try:
            closing_results = {
                'timestamp': datetime.now(self.ist_tz).strftime('%Y-%m-%d %H:%M:%S'),
                'closed_positions': [],
                'failed_closures': [],
                'total_closed': 0,
                'daily_pnl': 0.0,
                'closing_status': 'COMPLETED'
            }
            
            positions = active_positions.get('positions', [])
            
            if not positions:
                logger.info("No active positions to close")
                closing_results['closing_status'] = 'NO_POSITIONS'
                return closing_results
            
            logger.info(f"Closing {len(positions)} active positions")
            
            # Close each position
            for position in positions:
                close_result = await self._close_single_position(position)
                
                if close_result.get('success', False):
                    closing_results['closed_positions'].append(close_result)
                    closing_results['total_closed'] += 1
                    closing_results['daily_pnl'] += close_result.get('pnl', 0)
                else:
                    closing_results['failed_closures'].append(close_result)
            
            # Update daily statistics
            self.daily_pnl = closing_results['daily_pnl']
            self.total_trades = closing_results['total_closed']
            
            # Calculate win/loss statistics
            for position in closing_results['closed_positions']:
                if position.get('pnl', 0) > 0:
                    self.winning_trades += 1
                else:
                    self.losing_trades += 1
            
            logger.info(f"Position closing completed. "
                       f"Closed: {closing_results['total_closed']}, "
                       f"Daily P&L: {closing_results['daily_pnl']:.2f}")
            
            return closing_results
            
        except Exception as e:
            logger.error(f"Error closing positions: {e}")
            return {'closing_status': 'ERROR', 'error': str(e)}
    
    async def _close_single_position(self, position: Dict[str, Any]) -> Dict[str, Any]:
        """Close a single position and calculate P&L."""
        try:
            close_result = {
                'option_symbol': position.get('option_symbol'),
                'strike': position.get('strike'),
                'success': False,
                'timestamp': datetime.now(self.ist_tz).strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # Get current market price for P&L calculation
            current_price = await self._get_current_option_price(position.get('option_symbol'))
            entry_price = position.get('price', 0)
            quantity = position.get('quantity', 1)
            
            if not self.enable_live_trading:
                # Simulation mode
                pnl = (current_price - entry_price) * quantity * 75  # Assuming lot size of 75
                
                close_result.update({
                    'success': True,
                    'entry_price': entry_price,
                    'exit_price': current_price,
                    'quantity': quantity,
                    'pnl': round(pnl, 2),
                    'pnl_percentage': round((pnl / (entry_price * quantity * 75)) * 100, 2) if entry_price > 0 else 0,
                    'holding_period': self._calculate_holding_period(position),
                    'mode': 'SIMULATION'
                })
                
                # Store closed trade
                self.closed_trades.append(close_result)
                
                logger.info(f"Simulated position closed: {close_result['option_symbol']}, "
                           f"P&L: {close_result['pnl']:.2f}")
            else:
                # Live trading mode (placeholder for actual implementation)
                logger.warning("Live trading not implemented yet")
                close_result['error'] = 'Live trading not implemented'
            
            return close_result
            
        except Exception as e:
            logger.error(f"Error closing single position: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _get_current_option_price(self, option_symbol: str) -> float:
        """Get current market price for an option."""
        try:
            if not self.fyers_client:
                # Return simulated price for testing
                return 50.0  # Placeholder price
            
            # In real implementation, fetch current price from Fyers API
            # response = self.fyers_client.quotes({"symbols": option_symbol})
            # return response.get('d', [{}])[0].get('v', {}).get('lp', 0.0)
            
            return 50.0  # Placeholder
            
        except Exception as e:
            logger.error(f"Error getting current price for {option_symbol}: {e}")
            return 0.0
    
    def _calculate_holding_period(self, position: Dict[str, Any]) -> str:
        """Calculate holding period for a position."""
        try:
            entry_time_str = position.get('timestamp', '')
            if not entry_time_str:
                return 'Unknown'
            
            entry_time = datetime.strptime(entry_time_str, '%Y-%m-%d %H:%M:%S')
            current_time = datetime.now()
            
            holding_period = current_time - entry_time
            hours = holding_period.total_seconds() / 3600
            
            return f"{hours:.1f} hours"
            
        except Exception as e:
            logger.error(f"Error calculating holding period: {e}")
            return 'Unknown'
    
    def check_stop_loss_conditions(self, active_positions: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Check if any positions need to be closed due to stop-loss conditions.
        
        Args:
            active_positions: Dictionary of active positions
            
        Returns:
            List: Positions that need immediate closure
        """
        try:
            positions_to_close = []
            positions = active_positions.get('positions', [])
            
            for position in positions:
                # Calculate current P&L (simplified)
                entry_price = position.get('price', 0)
                current_price = 50.0  # Placeholder - should fetch real price
                quantity = position.get('quantity', 1)
                
                current_pnl = (current_price - entry_price) * quantity * 75
                
                # Check stop-loss conditions
                if current_pnl <= self.max_loss_threshold:
                    position['stop_loss_triggered'] = True
                    position['current_pnl'] = current_pnl
                    positions_to_close.append(position)
                    
                    logger.warning(f"Stop-loss triggered for {position.get('option_symbol')}: "
                                 f"P&L {current_pnl:.2f}")
            
            return positions_to_close
            
        except Exception as e:
            logger.error(f"Error checking stop-loss conditions: {e}")
            return []
    
    def generate_daily_trade_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive daily trade report.
        
        Returns:
            Dict: Daily trade report
        """
        try:
            report = {
                'date': datetime.now(self.ist_tz).strftime('%Y-%m-%d'),
                'timestamp': datetime.now(self.ist_tz).strftime('%Y-%m-%d %H:%M:%S'),
                'summary': {
                    'total_trades': self.total_trades,
                    'winning_trades': self.winning_trades,
                    'losing_trades': self.losing_trades,
                    'win_rate': 0.0,
                    'daily_pnl': self.daily_pnl,
                    'avg_pnl_per_trade': 0.0
                },
                'trade_details': self.closed_trades.copy(),
                'performance_metrics': {}
            }
            
            # Calculate performance metrics
            if self.total_trades > 0:
                report['summary']['win_rate'] = round(
                    (self.winning_trades / self.total_trades) * 100, 2
                )
                report['summary']['avg_pnl_per_trade'] = round(
                    self.daily_pnl / self.total_trades, 2
                )
            
            # Calculate additional metrics
            if self.closed_trades:
                pnls = [trade.get('pnl', 0) for trade in self.closed_trades]
                
                report['performance_metrics'] = {
                    'max_profit': max(pnls),
                    'max_loss': min(pnls),
                    'avg_profit': round(sum([p for p in pnls if p > 0]) / max(1, self.winning_trades), 2),
                    'avg_loss': round(sum([p for p in pnls if p < 0]) / max(1, self.losing_trades), 2),
                    'profit_factor': self._calculate_profit_factor(pnls)
                }
            
            logger.info(f"Daily trade report generated: {self.total_trades} trades, "
                       f"P&L: {self.daily_pnl:.2f}")
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating daily trade report: {e}")
            return {'error': str(e)}
    
    def _calculate_profit_factor(self, pnls: List[float]) -> float:
        """Calculate profit factor (gross profit / gross loss)."""
        try:
            gross_profit = sum([p for p in pnls if p > 0])
            gross_loss = abs(sum([p for p in pnls if p < 0]))
            
            if gross_loss == 0:
                return float('inf') if gross_profit > 0 else 0.0
            
            return round(gross_profit / gross_loss, 2)
            
        except Exception as e:
            logger.error(f"Error calculating profit factor: {e}")
            return 0.0
    
    def reset_daily_stats(self):
        """Reset daily statistics for new trading day."""
        try:
            self.closed_trades.clear()
            self.daily_pnl = 0.0
            self.total_trades = 0
            self.winning_trades = 0
            self.losing_trades = 0
            
            logger.info("Daily statistics reset for new trading day")
            
        except Exception as e:
            logger.error(f"Error resetting daily stats: {e}")
    
    def get_trade_summary(self) -> Dict[str, Any]:
        """Get current trade summary."""
        return {
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'daily_pnl': self.daily_pnl,
            'win_rate': round((self.winning_trades / max(1, self.total_trades)) * 100, 2),
            'closed_trades_count': len(self.closed_trades)
        }
    
    def update_config(self, new_config: dict):
        """Update trade closer configuration."""
        try:
            self.config.update(new_config)
            self.min_profit_threshold = self.config.get('min_profit_threshold', self.min_profit_threshold)
            self.max_loss_threshold = self.config.get('max_loss_threshold', self.max_loss_threshold)
            self.enable_live_trading = self.config.get('enable_live_trading', self.enable_live_trading)
            
            logger.info("Trade closer configuration updated")
            
        except Exception as e:
            logger.error(f"Error updating trade closer configuration: {e}")
