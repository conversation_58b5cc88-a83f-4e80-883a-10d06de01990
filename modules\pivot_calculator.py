"""
Enhanced Pivot Point Calculator Module.

This module extends the existing pivot point calculator to support:
1. Weekly and monthly pivot calculations
2. Weekly pivot point premiums for options
3. Last positive pivot point proximity logic
4. Historical pivot tracking
"""

import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pivot_points import pivot_point_standard

logger = logging.getLogger(__name__)


class EnhancedPivotCalculator:
    """
    Enhanced pivot point calculator with weekly/monthly support
    and premium calculation capabilities.
    """
    
    def __init__(self, config: dict):
        """
        Initialize the enhanced pivot calculator.
        
        Args:
            config: Configuration dictionary for pivot calculator settings
        """
        self.config = config
        self.calculation_type = config.get('calculation_type', 'WEEKLY')
        self.lookback_weeks = config.get('lookback_weeks', 4)
        self.proximity_percentage = config.get('proximity_percentage', 2.0)
        
        # Cache for historical pivot data
        self._pivot_history = {}
        
        logger.info(f"EnhancedPivotCalculator initialized with type: {self.calculation_type}")
    
    def calculate_standard_pivots(self, high: float, low: float, close: float) -> Dict[str, float]:
        """
        Calculate standard pivot points using existing function.
        
        Args:
            high: High price
            low: Low price
            close: Close price
            
        Returns:
            Dict: Pivot levels
        """
        return pivot_point_standard(high, low, close)
    
    def calculate_weekly_pivots(self, weekly_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Calculate weekly pivot points from weekly OHLC data.
        
        Args:
            weekly_data: Weekly OHLC data dictionary
            
        Returns:
            Dict: Weekly pivot levels
        """
        try:
            if not weekly_data or not all(k in weekly_data for k in ['high', 'low', 'close']):
                logger.error("Invalid weekly data for pivot calculation")
                return {}
            
            high = float(weekly_data['high'])
            low = float(weekly_data['low'])
            close = float(weekly_data['close'])
            
            logger.info(f"Calculating weekly pivots from H:{high:.2f} L:{low:.2f} C:{close:.2f}")
            
            pivots = self.calculate_standard_pivots(high, low, close)
            
            # Add metadata
            pivots['calculation_type'] = 'WEEKLY'
            pivots['date'] = weekly_data.get('date', datetime.now().strftime('%Y-%m-%d'))
            
            logger.info(f"Weekly pivot calculated: {pivots['Pivot']:.2f}")
            
            return pivots
            
        except Exception as e:
            logger.error(f"Error calculating weekly pivots: {e}")
            return {}
    
    def calculate_monthly_pivots(self, monthly_data: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate monthly pivot points from monthly OHLC data.
        
        Args:
            monthly_data: DataFrame with monthly OHLC data
            
        Returns:
            Dict: Monthly pivot levels
        """
        try:
            if monthly_data.empty:
                logger.error("Empty monthly data for pivot calculation")
                return {}
            
            # Get monthly high, low, close
            high = monthly_data['high'].max()
            low = monthly_data['low'].min()
            close = monthly_data['close'].iloc[-1]  # Last close
            
            logger.info(f"Calculating monthly pivots from H:{high:.2f} L:{low:.2f} C:{close:.2f}")
            
            pivots = self.calculate_standard_pivots(high, low, close)
            
            # Add metadata
            pivots['calculation_type'] = 'MONTHLY'
            pivots['date'] = monthly_data.index[-1].strftime('%Y-%m-%d')
            
            logger.info(f"Monthly pivot calculated: {pivots['Pivot']:.2f}")
            
            return pivots
            
        except Exception as e:
            logger.error(f"Error calculating monthly pivots: {e}")
            return {}
    
    def calculate_pivot_premiums(self, pivot_levels: Dict[str, float], 
                                current_price: float) -> Dict[str, float]:
        """
        Calculate pivot point premiums for options trading.
        
        Args:
            pivot_levels: Dictionary of pivot levels
            current_price: Current market price
            
        Returns:
            Dict: Pivot premiums and distances
        """
        try:
            if not pivot_levels or 'Pivot' not in pivot_levels:
                logger.error("Invalid pivot levels for premium calculation")
                return {}
            
            pivot = pivot_levels['Pivot']
            premiums = {}
            
            # Calculate distance from current price to each level
            for level_name, level_value in pivot_levels.items():
                if level_name in ['calculation_type', 'date']:
                    continue
                
                distance = level_value - current_price
                distance_percentage = (distance / current_price) * 100
                
                premiums[f"{level_name}_distance"] = round(distance, 2)
                premiums[f"{level_name}_distance_pct"] = round(distance_percentage, 2)
                premiums[f"{level_name}_premium"] = round(abs(distance), 2)
            
            # Find closest pivot levels
            closest_resistance = None
            closest_support = None
            min_resistance_distance = float('inf')
            min_support_distance = float('inf')
            
            for level_name, level_value in pivot_levels.items():
                if level_name.startswith('R') and level_value > current_price:
                    distance = level_value - current_price
                    if distance < min_resistance_distance:
                        min_resistance_distance = distance
                        closest_resistance = level_name
                
                elif level_name.startswith('S') and level_value < current_price:
                    distance = current_price - level_value
                    if distance < min_support_distance:
                        min_support_distance = distance
                        closest_support = level_name
            
            premiums['closest_resistance'] = closest_resistance
            premiums['closest_support'] = closest_support
            premiums['closest_resistance_distance'] = round(min_resistance_distance, 2)
            premiums['closest_support_distance'] = round(min_support_distance, 2)
            
            logger.info(f"Pivot premiums calculated. Closest R: {closest_resistance}, "
                       f"Closest S: {closest_support}")
            
            return premiums
            
        except Exception as e:
            logger.error(f"Error calculating pivot premiums: {e}")
            return {}
    
    def find_last_positive_pivot(self, symbol: str, historical_data: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        Find the last positive weekly pivot point for a symbol.
        
        Args:
            symbol: Trading symbol
            historical_data: Historical price data
            
        Returns:
            Dict: Last positive pivot information or None
        """
        try:
            if historical_data.empty:
                logger.error("Empty historical data for last positive pivot calculation")
                return None
            
            # Group data by weeks
            historical_data = historical_data.copy()
            historical_data['week'] = historical_data.index.to_period('W')
            
            weekly_groups = historical_data.groupby('week')
            positive_pivots = []
            
            for week, week_data in weekly_groups:
                if len(week_data) == 0:
                    continue
                
                # Calculate weekly OHLC
                weekly_high = week_data['high'].max()
                weekly_low = week_data['low'].min()
                weekly_close = week_data['close'].iloc[-1]
                weekly_open = week_data['open'].iloc[0]
                
                # Calculate pivot
                pivot_levels = self.calculate_standard_pivots(weekly_high, weekly_low, weekly_close)
                
                # Check if pivot is positive (above previous week's close)
                if len(positive_pivots) == 0:
                    # First week, consider it positive if pivot > open
                    if pivot_levels['Pivot'] > weekly_open:
                        positive_pivots.append({
                            'week': str(week),
                            'pivot': pivot_levels['Pivot'],
                            'high': weekly_high,
                            'low': weekly_low,
                            'close': weekly_close,
                            'levels': pivot_levels
                        })
                else:
                    # Compare with previous week's close
                    prev_week_data = historical_data[historical_data['week'] == week - 1]
                    if not prev_week_data.empty:
                        prev_close = prev_week_data['close'].iloc[-1]
                        if pivot_levels['Pivot'] > prev_close:
                            positive_pivots.append({
                                'week': str(week),
                                'pivot': pivot_levels['Pivot'],
                                'high': weekly_high,
                                'low': weekly_low,
                                'close': weekly_close,
                                'levels': pivot_levels
                            })
            
            if positive_pivots:
                last_positive = positive_pivots[-1]
                logger.info(f"Last positive pivot for {symbol}: {last_positive['pivot']:.2f} "
                           f"from week {last_positive['week']}")
                return last_positive
            else:
                logger.warning(f"No positive pivots found for {symbol}")
                return None
                
        except Exception as e:
            logger.error(f"Error finding last positive pivot for {symbol}: {e}")
            return None
    
    def calculate_pivot_proximity(self, pivot_level: float, current_price: float) -> Dict[str, Any]:
        """
        Calculate proximity metrics for pivot levels.
        
        Args:
            pivot_level: Pivot level value
            current_price: Current market price
            
        Returns:
            Dict: Proximity metrics
        """
        try:
            distance = abs(pivot_level - current_price)
            distance_percentage = (distance / current_price) * 100
            
            is_near = distance_percentage <= self.proximity_percentage
            
            proximity = {
                'distance': round(distance, 2),
                'distance_percentage': round(distance_percentage, 2),
                'is_near': is_near,
                'proximity_threshold': self.proximity_percentage
            }
            
            return proximity
            
        except Exception as e:
            logger.error(f"Error calculating pivot proximity: {e}")
            return {}
    
    def get_comprehensive_pivot_analysis(self, symbol: str, weekly_data: Dict[str, Any], 
                                       historical_data: pd.DataFrame, 
                                       current_price: float) -> Dict[str, Any]:
        """
        Get comprehensive pivot analysis for a symbol.
        
        Args:
            symbol: Trading symbol
            weekly_data: Weekly OHLC data
            historical_data: Historical price data
            current_price: Current market price
            
        Returns:
            Dict: Comprehensive pivot analysis
        """
        try:
            analysis = {
                'symbol': symbol,
                'current_price': current_price,
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # Calculate current week pivots
            if self.calculation_type == 'WEEKLY':
                current_pivots = self.calculate_weekly_pivots(weekly_data)
            else:
                current_pivots = self.calculate_monthly_pivots(historical_data)
            
            analysis['current_pivots'] = current_pivots
            
            # Calculate pivot premiums
            if current_pivots:
                premiums = self.calculate_pivot_premiums(current_pivots, current_price)
                analysis['pivot_premiums'] = premiums
            
            # Find last positive pivot
            last_positive = self.find_last_positive_pivot(symbol, historical_data)
            if last_positive:
                analysis['last_positive_pivot'] = last_positive
                
                # Calculate proximity to last positive pivot
                proximity = self.calculate_pivot_proximity(
                    last_positive['pivot'], current_price
                )
                analysis['last_positive_proximity'] = proximity
            
            logger.info(f"Comprehensive pivot analysis completed for {symbol}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in comprehensive pivot analysis for {symbol}: {e}")
            return {'symbol': symbol, 'error': str(e)}
    
    def store_pivot_history(self, symbol: str, pivot_data: Dict[str, Any]):
        """
        Store pivot data in history for future reference.
        
        Args:
            symbol: Trading symbol
            pivot_data: Pivot data to store
        """
        try:
            if symbol not in self._pivot_history:
                self._pivot_history[symbol] = []
            
            # Add timestamp
            pivot_data['stored_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            self._pivot_history[symbol].append(pivot_data)
            
            # Keep only recent history (last 50 entries)
            if len(self._pivot_history[symbol]) > 50:
                self._pivot_history[symbol] = self._pivot_history[symbol][-50:]
            
            logger.info(f"Stored pivot history for {symbol}")
            
        except Exception as e:
            logger.error(f"Error storing pivot history for {symbol}: {e}")
    
    def get_pivot_history(self, symbol: str) -> List[Dict[str, Any]]:
        """
        Get stored pivot history for a symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            List: Historical pivot data
        """
        return self._pivot_history.get(symbol, [])
