"""
Settings classes for the pivot point strategy application.
Each class represents a configuration section with validation and defaults.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Union
from datetime import time


@dataclass
class GeneralSettings:
    """General application settings."""
    env_path: str = "C:/Users/<USER>/Desktop/Python/.env"
    output_dir: str = "reports"
    timezone: str = "Asia/Kolkata"
    market_start_time: str = "09:15"
    market_end_time: str = "15:30"
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GeneralSettings':
        """Create settings from dictionary."""
        return cls(
            env_path=data.get('env_path', cls.env_path),
            output_dir=data.get('output_dir', cls.output_dir),
            timezone=data.get('timezone', cls.timezone),
            market_start_time=data.get('market_start_time', cls.market_start_time),
            market_end_time=data.get('market_end_time', cls.market_end_time)
        )


@dataclass
class DataFetcherSettings:
    """Data fetcher configuration settings."""
    candle_interval: int = 5
    historical_days: int = 30
    options_chain_fetch_current_week: bool = True
    options_chain_fetch_past_weeks: int = 2
    options_chain_max_strikes_per_side: int = 25
    refresh_interval: int = 300
    retry_attempts: int = 3
    retry_delay: int = 5
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DataFetcherSettings':
        """Create settings from dictionary."""
        options_chain = data.get('options_chain', {})
        
        return cls(
            candle_interval=data.get('candle_interval', cls.candle_interval),
            historical_days=data.get('historical_days', cls.historical_days),
            options_chain_fetch_current_week=options_chain.get('fetch_current_week', cls.options_chain_fetch_current_week),
            options_chain_fetch_past_weeks=options_chain.get('fetch_past_weeks', cls.options_chain_fetch_past_weeks),
            options_chain_max_strikes_per_side=options_chain.get('max_strikes_per_side', cls.options_chain_max_strikes_per_side),
            refresh_interval=data.get('refresh_interval', cls.refresh_interval),
            retry_attempts=data.get('retry_attempts', cls.retry_attempts),
            retry_delay=data.get('retry_delay', cls.retry_delay)
        )


@dataclass
class PivotCalculatorSettings:
    """Pivot calculator configuration settings."""
    weekly_pivots_enabled: bool = True
    weekly_pivots_method: str = "standard"
    monthly_pivots_enabled: bool = True
    monthly_pivots_method: str = "standard"
    weekly_pivot_premiums_enabled: bool = True
    weekly_pivot_premiums_calculation_method: str = "last_positive"
    weekly_pivot_premiums_proximity_percentage: float = 2.0
    last_positive_pivot_lookback_weeks: int = 4
    last_positive_pivot_minimum_distance: int = 50
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PivotCalculatorSettings':
        """Create settings from dictionary."""
        weekly_pivots = data.get('weekly_pivots', {})
        monthly_pivots = data.get('monthly_pivots', {})
        weekly_pivot_premiums = data.get('weekly_pivot_premiums', {})
        last_positive_pivot = data.get('last_positive_pivot', {})
        
        return cls(
            weekly_pivots_enabled=weekly_pivots.get('enabled', cls.weekly_pivots_enabled),
            weekly_pivots_method=weekly_pivots.get('method', cls.weekly_pivots_method),
            monthly_pivots_enabled=monthly_pivots.get('enabled', cls.monthly_pivots_enabled),
            monthly_pivots_method=monthly_pivots.get('method', cls.monthly_pivots_method),
            weekly_pivot_premiums_enabled=weekly_pivot_premiums.get('enabled', cls.weekly_pivot_premiums_enabled),
            weekly_pivot_premiums_calculation_method=weekly_pivot_premiums.get('calculation_method', cls.weekly_pivot_premiums_calculation_method),
            weekly_pivot_premiums_proximity_percentage=weekly_pivot_premiums.get('proximity_percentage', cls.weekly_pivot_premiums_proximity_percentage),
            last_positive_pivot_lookback_weeks=last_positive_pivot.get('lookback_weeks', cls.last_positive_pivot_lookback_weeks),
            last_positive_pivot_minimum_distance=last_positive_pivot.get('minimum_distance', cls.last_positive_pivot_minimum_distance)
        )


@dataclass
class DeltaFilterSettings:
    """Delta filter configuration settings."""
    min_delta: float = 0.30
    max_delta: float = 0.65
    max_ce_options: int = 25
    max_pe_options: int = 25
    prioritize_closest_to_money: bool = True
    exclude_deep_otm: bool = True
    exclude_deep_itm: bool = True
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DeltaFilterSettings':
        """Create settings from dictionary."""
        delta_range = data.get('delta_range', {})
        max_options = data.get('max_options_per_index', {})
        
        return cls(
            min_delta=delta_range.get('min_delta', cls.min_delta),
            max_delta=delta_range.get('max_delta', cls.max_delta),
            max_ce_options=max_options.get('ce_options', cls.max_ce_options),
            max_pe_options=max_options.get('pe_options', cls.max_pe_options),
            prioritize_closest_to_money=data.get('prioritize_closest_to_money', cls.prioritize_closest_to_money),
            exclude_deep_otm=data.get('exclude_deep_otm', cls.exclude_deep_otm),
            exclude_deep_itm=data.get('exclude_deep_itm', cls.exclude_deep_itm)
        )


@dataclass
class PivotPremiumFilterSettings:
    """Pivot premium filter configuration settings."""
    min_volume: int = 1000
    min_open_interest: int = 500
    min_price: float = 2.0
    max_price: float = 500.0
    deviation_percentage: float = 5.0
    use_absolute_points: bool = False
    absolute_points: int = 100
    premium_calculation_method: str = "theoretical"
    volatility_estimate: float = 0.20
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PivotPremiumFilterSettings':
        """Create settings from dictionary."""
        proximity_settings = data.get('proximity_settings', {})
        premium_calculation = data.get('premium_calculation', {})
        
        return cls(
            min_volume=data.get('min_volume', cls.min_volume),
            min_open_interest=data.get('min_open_interest', cls.min_open_interest),
            min_price=data.get('min_price', cls.min_price),
            max_price=data.get('max_price', cls.max_price),
            deviation_percentage=proximity_settings.get('deviation_percentage', cls.deviation_percentage),
            use_absolute_points=proximity_settings.get('use_absolute_points', cls.use_absolute_points),
            absolute_points=proximity_settings.get('absolute_points', cls.absolute_points),
            premium_calculation_method=premium_calculation.get('method', cls.premium_calculation_method),
            volatility_estimate=premium_calculation.get('volatility_estimate', cls.volatility_estimate)
        )


@dataclass
class ShortlistEngineSettings:
    """Shortlist engine configuration settings."""
    combination_method: str = "intersection"
    delta_filter_weight: float = 0.6
    pivot_filter_weight: float = 0.4
    max_final_options: int = 20
    remove_duplicates: bool = True
    sort_by: str = "combined_score"
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ShortlistEngineSettings':
        """Create settings from dictionary."""
        weights = data.get('weights', {})
        
        return cls(
            combination_method=data.get('combination_method', cls.combination_method),
            delta_filter_weight=weights.get('delta_filter', cls.delta_filter_weight),
            pivot_filter_weight=weights.get('pivot_filter', cls.pivot_filter_weight),
            max_final_options=data.get('max_final_options', cls.max_final_options),
            remove_duplicates=data.get('remove_duplicates', cls.remove_duplicates),
            sort_by=data.get('sort_by', cls.sort_by)
        )


@dataclass
class TradeAction:
    """Represents a single trade action in a sequence."""
    action: str  # "BUY" or "SELL"
    quantity: int
    order_type: str = "MARKET"


@dataclass
class StrategyExecutionSettings:
    """Strategy execution configuration settings."""
    market_analysis_time: str = "09:45"
    analysis_start_time: str = "09:15"
    analysis_end_time: str = "09:45"
    high_breach_sequence: List[TradeAction] = field(default_factory=list)
    low_breach_sequence: List[TradeAction] = field(default_factory=list)
    lot_sizes: Dict[str, int] = field(default_factory=dict)
    max_positions: int = 10
    max_loss_per_trade: float = 5000.0
    max_daily_loss: float = 20000.0
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StrategyExecutionSettings':
        """Create settings from dictionary."""
        execution_times = data.get('execution_times', {})
        breach_strategies = data.get('breach_strategies', {})
        risk_management = data.get('risk_management', {})
        
        # Parse trade sequences
        high_breach_sequence = []
        for action_data in breach_strategies.get('high_breach_sequence', []):
            high_breach_sequence.append(TradeAction(
                action=action_data.get('action', 'BUY'),
                quantity=action_data.get('quantity', 1),
                order_type=action_data.get('order_type', 'MARKET')
            ))
        
        low_breach_sequence = []
        for action_data in breach_strategies.get('low_breach_sequence', []):
            low_breach_sequence.append(TradeAction(
                action=action_data.get('action', 'SELL'),
                quantity=action_data.get('quantity', 1),
                order_type=action_data.get('order_type', 'MARKET')
            ))
        
        return cls(
            market_analysis_time=execution_times.get('market_analysis_time', cls.market_analysis_time),
            analysis_start_time=execution_times.get('analysis_start_time', cls.analysis_start_time),
            analysis_end_time=execution_times.get('analysis_end_time', cls.analysis_end_time),
            high_breach_sequence=high_breach_sequence,
            low_breach_sequence=low_breach_sequence,
            lot_sizes=data.get('lot_sizes', {}),
            max_positions=risk_management.get('max_positions', cls.max_positions),
            max_loss_per_trade=risk_management.get('max_loss_per_trade', cls.max_loss_per_trade),
            max_daily_loss=risk_management.get('max_daily_loss', cls.max_daily_loss)
        )


@dataclass
class TradeCloserSettings:
    """Trade closer configuration settings."""
    closing_time: str = "15:06"
    closing_method: str = "market_orders"
    emergency_closing_enabled: bool = True
    emergency_max_loss_threshold: float = 15000.0
    emergency_time_based_closing: bool = True
    pnl_include_brokerage: bool = True
    pnl_brokerage_per_trade: float = 20.0
    pnl_include_taxes: bool = True
    pnl_tax_rate: float = 0.18

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TradeCloserSettings':
        """Create settings from dictionary."""
        emergency_closing = data.get('emergency_closing', {})
        pnl_calculation = data.get('pnl_calculation', {})

        return cls(
            closing_time=data.get('closing_time', cls.closing_time),
            closing_method=data.get('closing_method', cls.closing_method),
            emergency_closing_enabled=emergency_closing.get('enabled', cls.emergency_closing_enabled),
            emergency_max_loss_threshold=emergency_closing.get('max_loss_threshold', cls.emergency_max_loss_threshold),
            emergency_time_based_closing=emergency_closing.get('time_based_closing', cls.emergency_time_based_closing),
            pnl_include_brokerage=pnl_calculation.get('include_brokerage', cls.pnl_include_brokerage),
            pnl_brokerage_per_trade=pnl_calculation.get('brokerage_per_trade', cls.pnl_brokerage_per_trade),
            pnl_include_taxes=pnl_calculation.get('include_taxes', cls.pnl_include_taxes),
            pnl_tax_rate=pnl_calculation.get('tax_rate', cls.pnl_tax_rate)
        )


@dataclass
class LoggingSettings:
    """Logging configuration settings."""
    console_level: str = "INFO"
    file_level: str = "DEBUG"
    log_directory: str = "logs"
    log_file_prefix: str = "pivot_strategy"
    max_log_files: int = 30
    module_levels: Dict[str, str] = field(default_factory=dict)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LoggingSettings':
        """Create settings from dictionary."""
        modules = data.get('modules', {})

        return cls(
            console_level=data.get('console_level', cls.console_level),
            file_level=data.get('file_level', cls.file_level),
            log_directory=data.get('log_directory', cls.log_directory),
            log_file_prefix=data.get('log_file_prefix', cls.log_file_prefix),
            max_log_files=data.get('max_log_files', cls.max_log_files),
            module_levels=modules
        )


@dataclass
class ReportingSettings:
    """Reporting configuration settings."""
    daily_reports_enabled: bool = True
    daily_report_time: str = "16:00"
    daily_include_pnl: bool = True
    daily_include_trades: bool = True
    daily_include_errors: bool = True
    formats: List[str] = field(default_factory=lambda: ["excel", "csv", "json"])
    content_summary_stats: bool = True
    content_detailed_trades: bool = True
    content_error_analysis: bool = True
    content_performance_metrics: bool = True
    email_notifications_enabled: bool = False
    email_smtp_server: str = "smtp.gmail.com"
    email_smtp_port: int = 587
    email_sender_email: str = ""
    email_sender_password: str = ""
    email_recipient_emails: List[str] = field(default_factory=list)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ReportingSettings':
        """Create settings from dictionary."""
        daily_reports = data.get('daily_reports', {})
        content = data.get('content', {})
        email_notifications = data.get('email_notifications', {})

        return cls(
            daily_reports_enabled=daily_reports.get('enabled', cls.daily_reports_enabled),
            daily_report_time=daily_reports.get('report_time', cls.daily_report_time),
            daily_include_pnl=daily_reports.get('include_pnl', cls.daily_include_pnl),
            daily_include_trades=daily_reports.get('include_trades', cls.daily_include_trades),
            daily_include_errors=daily_reports.get('include_errors', cls.daily_include_errors),
            formats=data.get('formats', cls.formats),
            content_summary_stats=content.get('summary_stats', cls.content_summary_stats),
            content_detailed_trades=content.get('detailed_trades', cls.content_detailed_trades),
            content_error_analysis=content.get('error_analysis', cls.content_error_analysis),
            content_performance_metrics=content.get('performance_metrics', cls.content_performance_metrics),
            email_notifications_enabled=email_notifications.get('enabled', cls.email_notifications_enabled),
            email_smtp_server=email_notifications.get('smtp_server', cls.email_smtp_server),
            email_smtp_port=email_notifications.get('smtp_port', cls.email_smtp_port),
            email_sender_email=email_notifications.get('sender_email', cls.email_sender_email),
            email_sender_password=email_notifications.get('sender_password', cls.email_sender_password),
            email_recipient_emails=email_notifications.get('recipient_emails', cls.email_recipient_emails)
        )


@dataclass
class ErrorHandlingSettings:
    """Error handling configuration settings."""
    api_max_retries: int = 3
    api_retry_delay: int = 5
    api_exponential_backoff: bool = True
    api_fallback_data_source: str = "cache"
    data_handle_missing_data: bool = True
    data_interpolation_method: str = "forward_fill"
    data_max_missing_percentage: int = 10
    market_detect_market_closure: bool = True
    market_handle_early_closure: bool = True
    market_weekend_handling: str = "skip"
    circuit_breakers_enabled: bool = True
    circuit_breakers_max_consecutive_failures: int = 5
    circuit_breakers_cooldown_period: int = 300
    notifications_critical_errors: bool = True
    notifications_api_failures: bool = True
    notifications_data_quality_issues: bool = False

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ErrorHandlingSettings':
        """Create settings from dictionary."""
        api_failures = data.get('api_failures', {})
        data_issues = data.get('data_issues', {})
        market_interruptions = data.get('market_interruptions', {})
        circuit_breakers = data.get('circuit_breakers', {})
        notifications = data.get('notifications', {})

        return cls(
            api_max_retries=api_failures.get('max_retries', cls.api_max_retries),
            api_retry_delay=api_failures.get('retry_delay', cls.api_retry_delay),
            api_exponential_backoff=api_failures.get('exponential_backoff', cls.api_exponential_backoff),
            api_fallback_data_source=api_failures.get('fallback_data_source', cls.api_fallback_data_source),
            data_handle_missing_data=data_issues.get('handle_missing_data', cls.data_handle_missing_data),
            data_interpolation_method=data_issues.get('interpolation_method', cls.data_interpolation_method),
            data_max_missing_percentage=data_issues.get('max_missing_percentage', cls.data_max_missing_percentage),
            market_detect_market_closure=market_interruptions.get('detect_market_closure', cls.market_detect_market_closure),
            market_handle_early_closure=market_interruptions.get('handle_early_closure', cls.market_handle_early_closure),
            market_weekend_handling=market_interruptions.get('weekend_handling', cls.market_weekend_handling),
            circuit_breakers_enabled=circuit_breakers.get('enabled', cls.circuit_breakers_enabled),
            circuit_breakers_max_consecutive_failures=circuit_breakers.get('max_consecutive_failures', cls.circuit_breakers_max_consecutive_failures),
            circuit_breakers_cooldown_period=circuit_breakers.get('cooldown_period', cls.circuit_breakers_cooldown_period),
            notifications_critical_errors=notifications.get('critical_errors', cls.notifications_critical_errors),
            notifications_api_failures=notifications.get('api_failures', cls.notifications_api_failures),
            notifications_data_quality_issues=notifications.get('data_quality_issues', cls.notifications_data_quality_issues)
        )


@dataclass
class FyersSettings:
    """Fyers API configuration settings."""
    client_id: str = ""
    redirect_uri: str = "https://www.google.co.in/"
    api_base_url: str = "https://api.fyers.in"
    api_timeout: int = 30
    api_rate_limit: int = 100
    data_max_symbols_per_request: int = 50
    data_cache_duration: int = 300
    log_path: str = "logs"
    debug_mode: bool = False

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FyersSettings':
        """Create settings from dictionary."""
        api_settings = data.get('api_settings', {})
        data_settings = data.get('data_settings', {})

        return cls(
            client_id=data.get('client_id', cls.client_id),
            redirect_uri=data.get('redirect_uri', cls.redirect_uri),
            api_base_url=api_settings.get('base_url', cls.api_base_url),
            api_timeout=api_settings.get('timeout', cls.api_timeout),
            api_rate_limit=api_settings.get('rate_limit', cls.api_rate_limit),
            data_max_symbols_per_request=data_settings.get('max_symbols_per_request', cls.data_max_symbols_per_request),
            data_cache_duration=data_settings.get('cache_duration', cls.data_cache_duration),
            log_path=data.get('log_path', cls.log_path),
            debug_mode=data.get('debug_mode', cls.debug_mode)
        )


@dataclass
class DevelopmentSettings:
    """Development and testing configuration settings."""
    testing_mode: bool = False
    simulation_enabled: bool = False
    simulation_initial_capital: float = 100000.0
    simulation_simulate_slippage: bool = True
    simulation_slippage_percentage: float = 0.1
    debug_save_intermediate_data: bool = True
    debug_verbose_logging: bool = False
    debug_profile_performance: bool = False

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DevelopmentSettings':
        """Create settings from dictionary."""
        simulation = data.get('simulation', {})
        debug = data.get('debug', {})

        return cls(
            testing_mode=data.get('testing_mode', cls.testing_mode),
            simulation_enabled=simulation.get('enabled', cls.simulation_enabled),
            simulation_initial_capital=simulation.get('initial_capital', cls.simulation_initial_capital),
            simulation_simulate_slippage=simulation.get('simulate_slippage', cls.simulation_simulate_slippage),
            simulation_slippage_percentage=simulation.get('slippage_percentage', cls.simulation_slippage_percentage),
            debug_save_intermediate_data=debug.get('save_intermediate_data', cls.debug_save_intermediate_data),
            debug_verbose_logging=debug.get('verbose_logging', cls.debug_verbose_logging),
            debug_profile_performance=debug.get('profile_performance', cls.debug_profile_performance)
        )
