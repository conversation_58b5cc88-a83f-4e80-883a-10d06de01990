"""
Simple configuration manager for the pivot point strategy application.
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class ConfigManager:
    """Simple configuration manager."""

    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = Path(config_path)
        self._config_data: Dict[str, Any] = {}
        self.load_config()

    def load_config(self) -> None:
        """Load configuration from YAML file."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self._config_data = yaml.safe_load(file)
            logger.info(f"Configuration loaded from {self.config_path}")
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise

    def get(self, key: str, default=None):
        """Get configuration value by key."""
        return self._config_data.get(key, default)

    def get_symbols(self) -> list:
        """Get list of trading symbols."""
        return self._config_data.get('symbols', ['NIFTY'])

    def get_fyers_config(self) -> dict:
        """Get Fyers API configuration."""
        return self._config_data.get('fyers', {})

# Global configuration manager instance
config_manager: Optional[ConfigManager] = None


def get_config_manager(config_path: str = "config.yaml") -> ConfigManager:
    """Get the global configuration manager instance."""
    global config_manager
    if config_manager is None:
        config_manager = ConfigManager(config_path)
    return config_manager
