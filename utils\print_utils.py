"""
Utility module for organized console output, especially for parallel processing.
"""
import threading
import sys
from typing import Optional, Dict, Any

# ANSI color codes
COLORS = {
    "black": "\033[30m",
    "red": "\033[31m",
    "green": "\033[32m",
    "yellow": "\033[33m",
    "blue": "\033[34m",
    "magenta": "\033[35m",
    "cyan": "\033[36m",
    "white": "\033[37m",
    "reset": "\033[0m",
    "bold": "\033[1m",
    "underline": "\033[4m"
}

# Thread-local storage to keep track of the current symbol for each thread
_thread_local = threading.local()

# Lock for synchronized printing
_print_lock = threading.Lock()

def set_current_symbol(symbol: str) -> None:
    """Set the current symbol for the current thread"""
    _thread_local.symbol = symbol

def get_current_symbol() -> Optional[str]:
    """Get the current symbol for the current thread"""
    return getattr(_thread_local, 'symbol', None)

def symbol_print(message: str, symbol: Optional[str] = None, end: str = '\n') -> None:
    """
    Print a message with the symbol prefix to avoid interleaved output in parallel processing.

    Parameters:
        message: The message to print
        symbol: The symbol to prefix the message with (defaults to thread's current symbol)
        end: The string to append at the end (default: newline)
    """
    # Use the provided symbol or get it from thread-local storage
    current_symbol = symbol or get_current_symbol()

    # Format the message with the symbol prefix if available
    if current_symbol:
        formatted_message = f"[{current_symbol}] {message}"
    else:
        formatted_message = message

    # Use a lock to ensure atomic printing
    with _print_lock:
        print(formatted_message, end=end)
        sys.stdout.flush()  # Ensure output is displayed immediately

def section_header(title: str, symbol: Optional[str] = None, width: int = 80) -> None:
    """
    Print a section header with the given title.

    Parameters:
        title: The title of the section
        symbol: The symbol to prefix the header with (defaults to thread's current symbol)
        width: The width of the header (default: 80)
    """
    # Use the provided symbol or get it from thread-local storage
    current_symbol = symbol or get_current_symbol()

    # Format the header
    if current_symbol:
        header = f"[{current_symbol}] {title}"
        separator = f"[{current_symbol}] {'-' * (width - len(current_symbol) - 4)}"
    else:
        header = title
        separator = '-' * width

    # Print the header with a separator
    with _print_lock:
        print(f"\n{separator}")
        print(header)
        print(f"{separator}\n")
        sys.stdout.flush()

def data_table(headers: list, rows: list, symbol: Optional[str] = None) -> None:
    """
    Print a formatted data table.

    Parameters:
        headers: List of column headers
        rows: List of rows, where each row is a list of values
        symbol: The symbol to prefix the table with (defaults to thread's current symbol)
    """
    if not rows:
        symbol_print("No data to display in table.", symbol)
        return

    # Use the provided symbol or get it from thread-local storage
    current_symbol = symbol or get_current_symbol()

    # Calculate column widths based on headers and data
    col_widths = [len(str(h)) for h in headers]
    for row in rows:
        for i, cell in enumerate(row):
            if i < len(col_widths):  # Ensure we don't go out of bounds
                col_widths[i] = max(col_widths[i], len(str(cell)))

    # Format the header row
    header_row = " | ".join(f"{str(h):<{col_widths[i]}}" for i, h in enumerate(headers))
    separator = "-" * (sum(col_widths) + 3 * (len(headers) - 1))  # Account for " | " between columns

    # Format the prefix if a symbol is provided
    prefix = f"[{current_symbol}] " if current_symbol else ""

    # Print the table
    with _print_lock:
        print(f"{prefix}{header_row}")
        print(f"{prefix}{separator}")

        for row in rows:
            formatted_row = " | ".join(f"{str(cell):<{col_widths[i]}}" for i, cell in enumerate(row) if i < len(col_widths))
            print(f"{prefix}{formatted_row}")

        print()  # Add a blank line after the table
        sys.stdout.flush()

def print_colored(message: str, color: str = "reset", bold: bool = False) -> None:
    """
    Print a message in the specified color.
    
    Args:
        message: The message to print
        color: The color to use (default: reset)
        bold: Whether to print in bold (default: False)
    """
    color_code = COLORS.get(color.lower(), COLORS["reset"])
    bold_code = COLORS["bold"] if bold else ""
    reset_code = COLORS["reset"]
    
    with _print_lock:
        print(f"{bold_code}{color_code}{message}{reset_code}")
        sys.stdout.flush()

def print_header(title: str, width: int = 80) -> None:
    """
    Print a colored header with the given title.
    
    Args:
        title: The title of the header
        width: The width of the header (default: 80)
    """
    padding = (width - len(title) - 2) // 2
    header = f"{' ' * padding} {title} {' ' * padding}"
    
    with _print_lock:
        print()
        print_colored("=" * width, "cyan", bold=True)
        print_colored(header, "cyan", bold=True)
        print_colored("=" * width, "cyan", bold=True)
        print()
        sys.stdout.flush()

def print_separator(char: str = "-", width: int = 80, color: str = "blue") -> None:
    """
    Print a colored separator line.
    
    Args:
        char: The character to use for the separator (default: -)
        width: The width of the separator (default: 80)
        color: The color to use (default: blue)
    """
    with _print_lock:
        print_colored(char * width, color)
        sys.stdout.flush()
