"""
Centralized logging configuration for the application.
This module provides a consistent logging setup across all modules.
"""
import logging
import os
from pathlib import Path
from datetime import datetime

# Default log directory
DEFAULT_LOG_DIR = "logs"

def setup_logging(log_dir=DEFAULT_LOG_DIR, log_level=logging.INFO, reset=False):
    """
    Set up logging configuration for the application.
    
    Args:
        log_dir: Directory to store log files
        log_level: Logging level (default: INFO)
        reset: Whether to reset existing loggers (default: False)
    
    Returns:
        logging.Logger: Configured logger instance
    """
    # Create logs directory if it doesn't exist
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)
    
    # Generate log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d")
    log_file = log_path / f"pivot_strategy_{timestamp}.log"
    
    # Configure root logger
    root_logger = logging.getLogger()
    
    # Reset handlers if requested
    if reset and root_logger.handlers:
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
    
    # Only add handlers if they don't exist
    if not root_logger.handlers:
        # Set logging level
        root_logger.setLevel(log_level)
        
        # Create file handler
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(log_level)
        
        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        
        # Create formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Add handlers to logger
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
    
    # Create and return application logger
    logger = logging.getLogger('pivot_strategy')
    logger.info(f"Logging initialized. Log file: {log_file}")
    
    return logger

def get_logger(name):
    """
    Get a logger with the specified name.
    
    Args:
        name: Logger name (usually __name__ of the calling module)
    
    Returns:
        logging.Logger: Logger instance
    """
    return logging.getLogger(name)
