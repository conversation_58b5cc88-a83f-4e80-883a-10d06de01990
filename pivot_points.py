"""
Module for calculating pivot points and support/resistance levels.
"""
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def pivot_point_standard(high: float, low: float, close: float) -> Dict[str, float]:
    """
    Calculates the Standard Pivot Point and support/resistance levels.

    Parameters:
    - high: float - High price
    - low: float - Low price
    - close: float - Close price

    Returns:
    A dictionary with the pivot point (P), resistance levels (R1, R2, R3, R4, R5),
    and support levels (S1, S2, S3, S4, S5)
    """
    # Calculate the range
    range_hl = high - low

    # Calculate pivot point
    pivot = (high + low + close) / 3

    # Calculate standard resistance and support levels (R1-R3, S1-S3)
    r1 = 2 * pivot - low
    s1 = 2 * pivot - high
    r2 = pivot + range_hl
    s2 = pivot - range_hl
    r3 = high + 2 * (pivot - low)
    s3 = low - 2 * (high - pivot)

    # Calculate extended resistance levels (R4, R5)
    # R4 = R3 + (R3 - R2) = R3 + range_hl
    r4 = r3 + range_hl
    # R5 = R4 + (R4 - R3) = R4 + range_hl
    r5 = r4 + range_hl

    # Calculate extended support levels (S4, S5)
    # S4 = S3 - (S2 - S3) = S3 - range_hl
    s4 = s3 - range_hl
    # S5 = S4 - (S3 - S4) = S4 - range_hl
    s5 = s4 - range_hl
    
    

    return {
        'Pivot': round(pivot, 2),
        'R1': round(r1, 2),
        'S1': round(s1, 2),
        'R2': round(r2, 2),
        'S2': round(s2, 2),
        'R3': round(r3, 2),
        'S3': round(s3, 2),
        'R4': round(r4, 2),
        'S4': round(s4, 2),
        'R5': round(r5, 2),
        'S5': round(s5, 2),
    }
