"""
Pivot Premium Filter Module for the Pivot Point Strategy Application.

This module provides pivot premium-based filtering capabilities:
1. Filters options based on proximity to pivot point premiums
2. Applies liquidity filters (volume, open interest)
3. Filters by minimum price thresholds
4. Calculates deviation from pivot levels
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)


class PivotPremiumFilter:
    """
    Filter options based on pivot point premiums and liquidity criteria.
    """
    
    def __init__(self, config: dict):
        """
        Initialize the pivot premium filter.
        
        Args:
            config: Configuration dictionary for pivot premium filter settings
        """
        self.config = config
        self.min_volume = config.get('min_volume', 1000)
        self.min_price = config.get('min_price', 2.0)
        self.deviation_percentage = config.get('deviation_percentage', 5.0)
        self.min_open_interest = config.get('min_open_interest', 500)
        self.max_options_per_type = config.get('max_options_per_type', 15)
        self.prioritize_high_volume = config.get('prioritize_high_volume', True)
        
        logger.info(f"PivotPremiumFilter initialized with min_volume: {self.min_volume}, "
                   f"min_price: {self.min_price}")
    
    def calculate_pivot_premiums(self, options_data: List[Dict[str, Any]], 
                                pivot_levels: Dict[str, float], 
                                current_price: float) -> List[Dict[str, Any]]:
        """
        Calculate pivot premiums for each option.
        
        Args:
            options_data: List of option data dictionaries
            pivot_levels: Dictionary of pivot levels
            current_price: Current spot price
            
        Returns:
            List: Options with calculated pivot premiums
        """
        try:
            if not pivot_levels or 'Pivot' not in pivot_levels:
                logger.error("Invalid pivot levels for premium calculation")
                return options_data
            
            enhanced_options = []
            
            for option in options_data:
                enhanced_option = option.copy()
                strike = option.get('strike', 0)
                
                if strike <= 0:
                    continue
                
                # Calculate distances to all pivot levels
                pivot_distances = {}
                closest_pivot_level = None
                min_distance = float('inf')
                
                for level_name, level_value in pivot_levels.items():
                    if level_name in ['calculation_type', 'date']:
                        continue
                    
                    distance = abs(strike - level_value)
                    distance_percentage = (distance / current_price) * 100
                    
                    pivot_distances[f"{level_name}_distance"] = round(distance, 2)
                    pivot_distances[f"{level_name}_distance_pct"] = round(distance_percentage, 2)
                    
                    # Track closest pivot level
                    if distance < min_distance:
                        min_distance = distance
                        closest_pivot_level = level_name
                
                # Add pivot premium information
                enhanced_option['pivot_distances'] = pivot_distances
                enhanced_option['closest_pivot_level'] = closest_pivot_level
                enhanced_option['closest_pivot_distance'] = round(min_distance, 2)
                enhanced_option['closest_pivot_distance_pct'] = round((min_distance / current_price) * 100, 2)
                
                # Calculate premium relative to closest pivot
                if closest_pivot_level:
                    pivot_value = pivot_levels[closest_pivot_level]
                    premium_deviation = abs(strike - pivot_value) / pivot_value * 100
                    enhanced_option['pivot_premium_deviation'] = round(premium_deviation, 2)
                
                enhanced_options.append(enhanced_option)
            
            logger.info(f"Calculated pivot premiums for {len(enhanced_options)} options")
            return enhanced_options
            
        except Exception as e:
            logger.error(f"Error calculating pivot premiums: {e}")
            return options_data
    
    def filter_by_liquidity(self, options_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Filter options based on liquidity criteria.
        
        Args:
            options_data: List of option data dictionaries
            
        Returns:
            List: Filtered options meeting liquidity criteria
        """
        try:
            filtered_options = []
            
            for option in options_data:
                # Check call option liquidity
                call_volume = option.get('call_volume', 0)
                call_oi = option.get('call_oi', 0)
                call_ltp = option.get('call_ltp', 0)
                
                call_meets_criteria = (
                    call_volume >= self.min_volume and
                    call_oi >= self.min_open_interest and
                    call_ltp >= self.min_price
                )
                
                # Check put option liquidity
                put_volume = option.get('put_volume', 0)
                put_oi = option.get('put_oi', 0)
                put_ltp = option.get('put_ltp', 0)
                
                put_meets_criteria = (
                    put_volume >= self.min_volume and
                    put_oi >= self.min_open_interest and
                    put_ltp >= self.min_price
                )
                
                # Include option if either call or put meets criteria
                if call_meets_criteria or put_meets_criteria:
                    # Add liquidity scores
                    option['call_liquidity_score'] = self._calculate_liquidity_score(
                        call_volume, call_oi, call_ltp
                    )
                    option['put_liquidity_score'] = self._calculate_liquidity_score(
                        put_volume, put_oi, put_ltp
                    )
                    option['overall_liquidity_score'] = max(
                        option['call_liquidity_score'], 
                        option['put_liquidity_score']
                    )
                    
                    filtered_options.append(option)
            
            logger.info(f"Liquidity filter: {len(filtered_options)} options passed from {len(options_data)}")
            return filtered_options
            
        except Exception as e:
            logger.error(f"Error filtering by liquidity: {e}")
            return options_data
    
    def filter_by_pivot_proximity(self, options_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Filter options based on proximity to pivot levels.
        
        Args:
            options_data: List of option data dictionaries with pivot distances
            
        Returns:
            List: Options near pivot levels
        """
        try:
            filtered_options = []
            
            for option in options_data:
                closest_distance_pct = option.get('closest_pivot_distance_pct', float('inf'))
                
                # Filter by deviation percentage
                if closest_distance_pct <= self.deviation_percentage:
                    option['is_near_pivot'] = True
                    filtered_options.append(option)
                else:
                    option['is_near_pivot'] = False
            
            logger.info(f"Pivot proximity filter: {len(filtered_options)} options within "
                       f"{self.deviation_percentage}% of pivot levels")
            return filtered_options
            
        except Exception as e:
            logger.error(f"Error filtering by pivot proximity: {e}")
            return options_data
    
    def apply_comprehensive_filter(self, options_data: List[Dict[str, Any]], 
                                  pivot_levels: Dict[str, float], 
                                  current_price: float) -> Dict[str, List[Dict[str, Any]]]:
        """
        Apply comprehensive pivot premium filtering.
        
        Args:
            options_data: List of option data dictionaries
            pivot_levels: Dictionary of pivot levels
            current_price: Current spot price
            
        Returns:
            Dict: Filtered options separated by type
        """
        try:
            logger.info(f"Starting comprehensive pivot premium filter for {len(options_data)} options")
            
            # Step 1: Calculate pivot premiums
            options_with_premiums = self.calculate_pivot_premiums(
                options_data, pivot_levels, current_price
            )
            
            # Step 2: Filter by liquidity
            liquid_options = self.filter_by_liquidity(options_with_premiums)
            
            # Step 3: Filter by pivot proximity
            pivot_filtered_options = self.filter_by_pivot_proximity(liquid_options)
            
            # Step 4: Separate calls and puts
            filtered_calls = []
            filtered_puts = []
            
            for option in pivot_filtered_options:
                # Check if call option meets all criteria
                if (option.get('call_symbol') and 
                    option.get('call_ltp', 0) >= self.min_price and
                    option.get('call_volume', 0) >= self.min_volume):
                    filtered_calls.append(option)
                
                # Check if put option meets all criteria
                if (option.get('put_symbol') and 
                    option.get('put_ltp', 0) >= self.min_price and
                    option.get('put_volume', 0) >= self.min_volume):
                    filtered_puts.append(option)
            
            # Step 5: Sort and limit results
            if self.prioritize_high_volume:
                filtered_calls.sort(key=lambda x: x.get('call_volume', 0), reverse=True)
                filtered_puts.sort(key=lambda x: x.get('put_volume', 0), reverse=True)
            else:
                # Sort by closest to pivot levels
                filtered_calls.sort(key=lambda x: x.get('closest_pivot_distance_pct', float('inf')))
                filtered_puts.sort(key=lambda x: x.get('closest_pivot_distance_pct', float('inf')))
            
            # Limit number of options
            filtered_calls = filtered_calls[:self.max_options_per_type]
            filtered_puts = filtered_puts[:self.max_options_per_type]
            
            logger.info(f"Comprehensive filter results: {len(filtered_calls)} calls, {len(filtered_puts)} puts")
            
            return {
                'calls': filtered_calls,
                'puts': filtered_puts,
                'total_processed': len(options_data),
                'total_filtered': len(filtered_calls) + len(filtered_puts)
            }
            
        except Exception as e:
            logger.error(f"Error in comprehensive pivot premium filter: {e}")
            return {'calls': [], 'puts': [], 'total_processed': 0, 'total_filtered': 0}
    
    def _calculate_liquidity_score(self, volume: int, open_interest: int, ltp: float) -> float:
        """
        Calculate liquidity score for an option.
        
        Args:
            volume: Trading volume
            open_interest: Open interest
            ltp: Last traded price
            
        Returns:
            float: Liquidity score
        """
        try:
            # Normalize components
            volume_score = min(volume / 10000, 1.0)  # Max score at 10k volume
            oi_score = min(open_interest / 50000, 1.0)  # Max score at 50k OI
            price_score = min(ltp / 100, 1.0)  # Max score at 100 price
            
            # Weighted average
            liquidity_score = (volume_score * 0.4 + oi_score * 0.4 + price_score * 0.2)
            
            return round(liquidity_score, 3)
            
        except Exception as e:
            logger.error(f"Error calculating liquidity score: {e}")
            return 0.0
    
    def get_filter_statistics(self, filtered_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get statistics about filtering results.
        
        Args:
            filtered_results: Results from comprehensive filter
            
        Returns:
            Dict: Filter statistics
        """
        try:
            calls = filtered_results.get('calls', [])
            puts = filtered_results.get('puts', [])
            
            stats = {
                'total_calls': len(calls),
                'total_puts': len(puts),
                'total_options': len(calls) + len(puts),
                'filter_efficiency': 0.0,
                'avg_call_liquidity': 0.0,
                'avg_put_liquidity': 0.0,
                'pivot_level_distribution': {}
            }
            
            # Calculate filter efficiency
            total_processed = filtered_results.get('total_processed', 0)
            if total_processed > 0:
                stats['filter_efficiency'] = round(
                    (stats['total_options'] / total_processed) * 100, 2
                )
            
            # Calculate average liquidity scores
            if calls:
                call_liquidity_scores = [opt.get('call_liquidity_score', 0) for opt in calls]
                stats['avg_call_liquidity'] = round(np.mean(call_liquidity_scores), 3)
            
            if puts:
                put_liquidity_scores = [opt.get('put_liquidity_score', 0) for opt in puts]
                stats['avg_put_liquidity'] = round(np.mean(put_liquidity_scores), 3)
            
            # Analyze pivot level distribution
            all_options = calls + puts
            pivot_levels = {}
            for option in all_options:
                closest_level = option.get('closest_pivot_level')
                if closest_level:
                    pivot_levels[closest_level] = pivot_levels.get(closest_level, 0) + 1
            
            stats['pivot_level_distribution'] = pivot_levels
            
            return stats
            
        except Exception as e:
            logger.error(f"Error calculating filter statistics: {e}")
            return {}
    
    def update_config(self, new_config: dict):
        """
        Update filter configuration.
        
        Args:
            new_config: New configuration dictionary
        """
        try:
            self.config.update(new_config)
            self.min_volume = self.config.get('min_volume', self.min_volume)
            self.min_price = self.config.get('min_price', self.min_price)
            self.deviation_percentage = self.config.get('deviation_percentage', self.deviation_percentage)
            self.min_open_interest = self.config.get('min_open_interest', self.min_open_interest)
            
            logger.info(f"Pivot premium filter configuration updated")
            
        except Exception as e:
            logger.error(f"Error updating pivot premium filter configuration: {e}")
    
    def get_filter_summary(self) -> Dict[str, Any]:
        """
        Get summary of current filter settings.
        
        Returns:
            Dict: Filter summary
        """
        return {
            'min_volume': self.min_volume,
            'min_price': self.min_price,
            'deviation_percentage': self.deviation_percentage,
            'min_open_interest': self.min_open_interest,
            'max_options_per_type': self.max_options_per_type,
            'prioritize_high_volume': self.prioritize_high_volume
        }
