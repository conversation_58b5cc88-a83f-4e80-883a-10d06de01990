"""
Utility functions for handling strike prices.
"""
import math
from symbol_config import get_symbol_config

def round_to_nearest_strike(price, symbol):
    """
    Round a price to the nearest valid strike price for the given symbol.
    
    Parameters:
        price: The price to round
        symbol: Trading symbol (e.g., 'NIFTY', 'BANKNIFTY')
        
    Returns:
        Rounded price to the nearest valid strike
    """
    # Get strike interval for the symbol
    strike_interval = get_symbol_config(symbol)["strike_interval"]
    
    # Round to the nearest interval
    return round(price / strike_interval) * strike_interval

def get_nearest_strikes(price, symbol, num_strikes=5):
    """
    Get a list of nearest valid strike prices for the given symbol.
    
    Parameters:
        price: The reference price
        symbol: Trading symbol (e.g., 'NIFTY', 'BANKNIFTY')
        num_strikes: Number of strikes to return on each side (default: 5)
        
    Returns:
        List of valid strike prices centered around the reference price
    """
    # Get strike interval for the symbol
    strike_interval = get_symbol_config(symbol)["strike_interval"]
    
    # Round to the nearest valid strike
    center_strike = round_to_nearest_strike(price, symbol)
    
    # Generate strikes above and below
    strikes = []
    for i in range(-num_strikes, num_strikes + 1):
        strike = center_strike + (i * strike_interval)
        strikes.append(strike)
    
    return sorted(strikes)

def align_pivot_levels(pivot_levels, symbol):
    """
    Align pivot point and support/resistance levels to valid strike prices.
    
    Parameters:
        pivot_levels: Dictionary with pivot point and support/resistance levels
        symbol: Trading symbol (e.g., 'NIFTY', 'BANKNIFTY')
        
    Returns:
        Dictionary with aligned levels
    """
    aligned_levels = {}
    
    for level_name, level_value in pivot_levels.items():
        aligned_levels[level_name] = round_to_nearest_strike(level_value, symbol)
        
    return aligned_levels

def get_bracket_strikes(price, symbol):
    """
    Get the strikes immediately above and below the given price.
    
    Parameters:
        price: The reference price
        symbol: Trading symbol (e.g., 'NIFTY', 'BANKNIFTY')
        
    Returns:
        Tuple of (lower_strike, upper_strike)
    """
    # Get strike interval for the symbol
    strike_interval = get_symbol_config(symbol)["strike_interval"]
    
    # Calculate lower and upper strikes
    lower_strike = math.floor(price / strike_interval) * strike_interval
    upper_strike = math.ceil(price / strike_interval) * strike_interval
    
    # If price is already at a valid strike, return the same value for both
    if lower_strike == upper_strike:
        return lower_strike, lower_strike + strike_interval
    
    return lower_strike, upper_strike
