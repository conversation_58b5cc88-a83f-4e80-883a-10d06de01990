"""
Module for fetching and processing option chain data using Fyers API.
"""
import logging
from datetime import datetime
import sys
import os

# Add parent directory to path to import config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import StrategyConfig

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import market data functions
from .market_data import get_last_trading_day_ohlc, get_weekly_pivot_ohlc_data

def fetch_nifty_option_chain(symbol="NIFTY", expiry_type="weekly", monthly_expiry="both",
                     angel_connect=None, pivot_calculation_type="daily", fyers=None,
                     spot_price=None):
    """
    Fetch option chain data from Fyers API

    Parameters:
        symbol: str - Symbol to fetch option chain for (e.g., 'NIFTY', 'BANKNIFTY')
        expiry_type: str - Type of expiry to fetch ('weekly' or 'monthly')
        monthly_expiry: str - Which monthly expiry to use ('current', 'next', 'both')
        angel_connect: AngelOneConnect instance (optional) - Not used, kept for backward compatibility
        pivot_calculation_type: str - Type of pivot calculation ('daily', 'weekly')
        fyers: FyersConnect instance (optional) - Used to fetch market data
        spot_price: float - Current spot price

    Returns:
        A list of dictionaries containing option chain data

    Raises:
        Exception: If there's an error fetching data from Fyers API
    """
    try:
        # Check if Fyers instance is available
        if fyers is None:
            raise Exception("Fyers connection is required to fetch option chain data")

        # Fetch option chain data from Fyers API
        options_data = fyers.get_option_chain(symbol, expiry_type, monthly_expiry)

        if options_data:
            logger.info(f"Successfully fetched option chain data from Fyers for {symbol}")
            logger.info(f"Using expiry type: {expiry_type.upper()}")
            if expiry_type.upper() == "MONTHLY":
                logger.info(f"Monthly expiry setting: {monthly_expiry.upper()}")
            return options_data
        else:
            raise Exception(f"No option chain data returned from Fyers API for {symbol}")

    except Exception as e:
        logger.error(f"Error fetching option chain data from Fyers: {str(e)}")
        logger.error("Failed to fetch real-time option chain data from Fyers API")
        # Return empty list instead of raising exception
        return []


