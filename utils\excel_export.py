"""
Module for exporting data to Excel and CSV.
"""
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Union
from data.market_data import OHLCData
import sys
import os
import logging
from pathlib import Path

# Add parent directory to path to import config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import ExcelConfig, DEFAULT_OUTPUT_DIR

# Configure logging - use existing logger instead of creating a new one
logger = logging.getLogger(__name__)

def export_dataframe_to_csv(df, filename, directory=DEFAULT_OUTPUT_DIR):
    """
    Export a pandas DataFrame to a CSV file in the specified directory.

    Parameters:
        df: pandas DataFrame to export
        filename: Name of the CSV file (without path)
        directory: Directory to save the file in (default: DEFAULT_OUTPUT_DIR)

    Returns:
        str: Path to the generated CSV file
    """
    # Ensure directory exists
    output_dir = Path(directory)
    output_dir.mkdir(exist_ok=True)

    # Create full path for the CSV file
    file_path = output_dir / filename

    # Export to CSV
    df.to_csv(file_path, index=False)

    logger.info(f"CSV file generated: {file_path}")
    return str(file_path)

def export_to_excel(all_options: List[Dict[str, Any]],
                   nearby_options: List[Dict[str, Any]],
                   trading_options: List[Dict[str, Any]],
                   pivot: float,
                   distance_threshold_pct: float,  # Used in the function description but not in the code
                   pivot_levels: Dict[str, float],
                   index_ohlc_data: OHLCData,
                   symbol_ohlc_data: Dict[str, OHLCData],
                   symbol_name: str = "nifty") -> str:
    """
    Export options data to Excel file with four sheets:
    1. Pivot Details - pivot point and support/resistance levels with calculation date
    2. Trading Options - filtered options meeting all criteria
    3. Nearby Options - all options within distance threshold of pivot
    4. All Options - complete list of all available options

    Parameters:
        all_options: List of all option dictionaries
        nearby_options: List of options within distance threshold
        trading_options: List of options meeting trading criteria
        pivot: Pivot point value
        distance_threshold_pct: Distance threshold percentage
        pivot_levels: Dictionary containing pivot, resistance and support levels
        index_ohlc_data: Dictionary containing OHLC data for the index with date
        symbol_ohlc_data: Dictionary containing OHLC data for individual option symbols

    Returns:
        str: Path to the generated Excel file
    """
    # Create a timestamp for the filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    # Use the symbol name in the filename
    symbol_name = symbol_name.upper()  # Convert to uppercase for consistency
    filename = f"{symbol_name}_options_analysis_{timestamp}.xlsx"

    # Ensure reports directory exists
    reports_dir = Path(DEFAULT_OUTPUT_DIR)
    reports_dir.mkdir(exist_ok=True)

    # Create full path for the Excel file
    file_path = reports_dir / filename

    # Create a Pandas Excel writer
    writer = pd.ExcelWriter(file_path, engine='xlsxwriter')

    # Round index OHLC values to 2 decimal places for the Pivot Details sheet
    open_val = round(index_ohlc_data.open, 2)
    high_val = round(index_ohlc_data.high, 2)
    low_val = round(index_ohlc_data.low, 2)
    close_val = round(index_ohlc_data.close, 2)
    volume_val = index_ohlc_data.volume if index_ohlc_data.volume > 0 else 'N/A'
    date_val = index_ohlc_data.date

    # Round pivot levels to 2 decimal places
    pivot_val = round(pivot_levels.get('Pivot', pivot), 2)
    r1_val = round(pivot_levels.get('R1', 0), 2)
    r2_val = round(pivot_levels.get('R2', 0), 2)
    r3_val = round(pivot_levels.get('R3', 0), 2)
    r4_val = round(pivot_levels.get('R4', 0), 2)
    r5_val = round(pivot_levels.get('R5', 0), 2)
    s1_val = round(pivot_levels.get('S1', 0), 2)
    s2_val = round(pivot_levels.get('S2', 0), 2)
    s3_val = round(pivot_levels.get('S3', 0), 2)
    s4_val = round(pivot_levels.get('S4', 0), 2)
    s5_val = round(pivot_levels.get('S5', 0), 2)

    # Create Pivot Details sheet first
    # Prepare data for pivot details
    pivot_data = [
        ['Date', date_val],
        ['Open', open_val],
        ['High', high_val],
        ['Low', low_val],
        ['Close', close_val],
        ['Volume', volume_val],
        ['', ''],  # Empty row for spacing
        ['Pivot Point', pivot_val],
        ['', ''],  # Empty row for spacing
        ['Resistance Levels', ''],
        ['R1', r1_val],
        ['R2', r2_val],
        ['R3', r3_val],
        ['R4', r4_val],
        ['R5', r5_val],
        ['', ''],  # Empty row for spacing
        ['Support Levels', ''],
        ['S1', s1_val],
        ['S2', s2_val],
        ['S3', s3_val],
        ['S4', s4_val],
        ['S5', s5_val],
    ]

    # Create DataFrame for pivot details
    df_pivot = pd.DataFrame(pivot_data, columns=ExcelConfig.PIVOT_COLUMNS)
    df_pivot.to_excel(writer, sheet_name=ExcelConfig.SHEET_NAMES['pivot'], index=False)

    # Add OHLC data to each option
    # Convert all options to DataFrame and write to Excel
    # First, we need to flatten the options data
    all_options_flat = []
    for opt in all_options:
        # Add call option
        # Get symbol-specific OHLC data for call option
        call_symbol = opt['call_symbol']
        # Create a default OHLCData object if the symbol is not found
        default_ohlc = OHLCData(open=0.0, high=0.0, low=0.0, close=0.0, date='', volume=0.0)
        call_ohlc = symbol_ohlc_data.get(call_symbol, default_ohlc)

        all_options_flat.append({
            'symbol': call_symbol,
            'token': call_ohlc.token,
            'type': 'call',
            'strike': round(opt['strike'], 2),
            'price': round(opt['call_price'], 2),
            'delta': round(opt.get('call_delta', 0.0), 2),
            'api_delta': round(0.0 if opt.get('call_api_delta') is None else opt.get('call_api_delta', 0.0), 2),
            'dist_pct': round(abs(opt['strike'] - pivot) / opt['strike'] * 100, 2),
            'change': round(opt['call_change'], 2),
            'volume': opt['call_volume'],
            'open': round(call_ohlc.open, 2),
            'high': round(call_ohlc.high, 2),
            'low': round(call_ohlc.low, 2),
            'close': round(call_ohlc.close, 2)
        })

        # Add put option
        # Get symbol-specific OHLC data for put option
        put_symbol = opt['put_symbol']
        # Create a default OHLCData object if the symbol is not found
        put_ohlc = symbol_ohlc_data.get(put_symbol, default_ohlc)

        all_options_flat.append({
            'symbol': put_symbol,
            'token': put_ohlc.token,
            'type': 'put',
            'strike': round(opt['strike'], 2),
            'price': round(opt['put_price'], 2),
            'delta': round(opt['put_delta'], 2),
            'api_delta': round(0.0 if opt.get('put_api_delta') is None else opt.get('put_api_delta', 0.0), 2),
            'dist_pct': round(abs(opt['strike'] - pivot) / opt['strike'] * 100, 2),
            'change': round(opt['put_change'], 2),
            'volume': opt['put_volume'],
            'open': round(put_ohlc.open, 2),
            'high': round(put_ohlc.high, 2),
            'low': round(put_ohlc.low, 2),
            'close': round(put_ohlc.close, 2)
        })

    # Sort by strike price and option type
    all_options_flat.sort(key=lambda x: (x['strike'], x['type']))

    # Convert to DataFrame and write to Excel
    df_all = pd.DataFrame(all_options_flat)
    df_all = df_all[ExcelConfig.OPTION_COLUMNS]
    df_all.to_excel(writer, sheet_name=ExcelConfig.SHEET_NAMES['all_options'], index=False)

    # Process nearby options with rounded values and OHLC data
    nearby_options_processed = []
    for opt in nearby_options:
        # Get symbol-specific OHLC data
        symbol = opt['symbol']
        # Create a default OHLCData object if the symbol is not found
        default_ohlc = OHLCData(open=0.0, high=0.0, low=0.0, close=0.0, date='', volume=0.0)
        symbol_ohlc = symbol_ohlc_data.get(symbol, default_ohlc)

        nearby_options_processed.append({
            'symbol': symbol,
            'token': symbol_ohlc.token,
            'type': opt['type'],
            'strike': round(opt['strike'], 2),
            'price': round(opt['price'], 2),
            'delta': round(opt['delta'], 2),
            'api_delta': round(0.0 if opt.get('api_delta') is None else opt.get('api_delta', 0.0), 2),
            'dist_pct': round(opt['dist_pct'], 2),
            'change': round(opt['change'], 2),
            'volume': opt['volume'],
            'open': round(symbol_ohlc.open, 2),
            'high': round(symbol_ohlc.high, 2),
            'low': round(symbol_ohlc.low, 2),
            'close': round(symbol_ohlc.close, 2)
        })

    # Convert nearby options to DataFrame and write to Excel
    if nearby_options_processed:
        df_nearby = pd.DataFrame(nearby_options_processed)
        df_nearby = df_nearby[ExcelConfig.OPTION_COLUMNS]
        df_nearby.to_excel(writer, sheet_name=ExcelConfig.SHEET_NAMES['nearby_options'], index=False)
    else:
        # Create an empty DataFrame with the same columns
        df_nearby = pd.DataFrame(columns=ExcelConfig.OPTION_COLUMNS)
        df_nearby.to_excel(writer, sheet_name=ExcelConfig.SHEET_NAMES['nearby_options'], index=False)

    # Process trading options with rounded values and OHLC data
    if trading_options:
        trading_options_processed = []
        for opt in trading_options:
            # Get symbol-specific OHLC data
            symbol = opt['symbol']
            # Create a default OHLCData object if the symbol is not found
            default_ohlc = OHLCData(open=0.0, high=0.0, low=0.0, close=0.0, date='', volume=0.0)
            symbol_ohlc = symbol_ohlc_data.get(symbol, default_ohlc)

            trading_options_processed.append({
                'symbol': symbol,
                'token': symbol_ohlc.token,
                'type': opt['type'],
                'strike': round(opt['strike'], 2),
                'price': round(opt['price'], 2),
                'delta': round(opt['delta'], 2),
                'api_delta': round(0.0 if opt.get('api_delta') is None else opt.get('api_delta', 0.0), 2),
                'dist_pct': round(opt['dist_pct'], 2),
                'change': round(opt.get('change', 0.0), 2),
                'volume': opt['volume'],
                'open': round(symbol_ohlc.open, 2),
                'high': round(symbol_ohlc.high, 2),
                'low': round(symbol_ohlc.low, 2),
                'close': round(symbol_ohlc.close, 2)
            })
        df_trading = pd.DataFrame(trading_options_processed)
        df_trading = df_trading[ExcelConfig.TRADING_COLUMNS]
        df_trading.to_excel(writer, sheet_name=ExcelConfig.SHEET_NAMES['trading_options'], index=False)
    else:
        # Create an empty DataFrame with the same columns
        df_trading = pd.DataFrame(columns=ExcelConfig.TRADING_COLUMNS)
        df_trading.to_excel(writer, sheet_name=ExcelConfig.SHEET_NAMES['trading_options'], index=False)

    # Format the Excel file
    workbook = writer.book

    # Add formats
    header_format = workbook.add_format({
        'bold': True,
        'text_wrap': True,
        'valign': 'top',
        'fg_color': '#D7E4BC',
        'border': 1
    })

    # Add number format for 2 decimal places
    num_format = workbook.add_format({'num_format': '0.00'})

    # Format each worksheet
    for sheet_name in writer.sheets:
        worksheet = writer.sheets[sheet_name]

        if sheet_name == ExcelConfig.SHEET_NAMES['pivot']:
            # Format the Pivot Details sheet
            worksheet.set_column('A:A', 20)  # Metric column
            worksheet.set_column('B:B', 15, num_format)  # Value column with number format

            # Add title format
            title_format = workbook.add_format({
                'bold': True,
                'font_size': 12,
                'bg_color': '#D7E4BC',
                'border': 1,
                'num_format': '0.00'
            })

            # Add section format
            section_format = workbook.add_format({
                'bold': True,
                'italic': True,
                'bg_color': '#F2F2F2'
            })

            # Apply formats to specific rows
            worksheet.write(0, 0, 'Metric', header_format)
            worksheet.write(0, 1, 'Value', header_format)

            # Format section headers
            worksheet.write(9, 0, 'Resistance Levels', section_format)
            worksheet.write(16, 0, 'Support Levels', section_format)

            # Format pivot point row
            worksheet.write(7, 0, 'Pivot Point', title_format)
            worksheet.write(7, 1, round(pivot_levels.get('Pivot', pivot), 2), title_format)

        else:
            # Format the other sheets
            if sheet_name == ExcelConfig.SHEET_NAMES['trading_options']:
                columns = ExcelConfig.TRADING_COLUMNS
            else:  # Nearby Options or All Options
                columns = ExcelConfig.OPTION_COLUMNS

            # Apply header format
            for col_num, value in enumerate(columns):
                worksheet.write(0, col_num, value, header_format)

            # Set column widths
            worksheet.set_column('A:A', 25)  # Symbol column
            worksheet.set_column('B:B', 15)  # Token column
            worksheet.set_column('C:C', 8)   # Type column
            worksheet.set_column('D:D', 10, num_format)  # Strike column with number format
            worksheet.set_column('E:E', 10, num_format)  # Price column with number format
            worksheet.set_column('F:F', 10, num_format)  # Delta column with number format
            worksheet.set_column('G:G', 10, num_format)  # API Delta column with number format
            worksheet.set_column('H:H', 10, num_format)  # Dist% column with number format

            # All sheets now have the same columns
            worksheet.set_column('I:I', 10, num_format)  # Change column with number format
            worksheet.set_column('J:J', 15)  # Volume column
            worksheet.set_column('K:K', 10, num_format)  # Open column with number format
            worksheet.set_column('L:L', 10, num_format)  # High column with number format
            worksheet.set_column('M:M', 10, num_format)  # Low column with number format
            worksheet.set_column('N:N', 10, num_format)  # Close column with number format

    # Save the Excel file
    writer.close()

    logger.info(f"Excel report generated: {file_path}")
    return str(file_path)
