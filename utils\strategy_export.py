"""
Module for exporting strategy results to Excel.
"""
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Union
from pathlib import Path
import os
import sys

# Add parent directory to path to import config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import DEFAULT_OUTPUT_DIR
from utils.logging_config import get_logger

# Get logger for this module
logger = get_logger(__name__)

def export_strategy_results(breakout_results: List[Dict[str, Any]], 
                           reversal_results: List[Dict[str, Any]],
                           output_dir: str = DEFAULT_OUTPUT_DIR) -> str:
    """
    Export strategy results to Excel.
    
    Args:
        breakout_results: List of breakout strategy results
        reversal_results: List of reversal strategy results
        output_dir: Directory to save the Excel file
    
    Returns:
        str: Path to the generated Excel file
    """
    # Create timestamp for filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"pivot_strategies_{timestamp}.xlsx"
    
    # Ensure output directory exists
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Create full path for the Excel file
    file_path = output_path / filename
    
    # Create a Pandas Excel writer
    writer = pd.ExcelWriter(file_path, engine='xlsxwriter')
    
    # Convert results to DataFrames
    if breakout_results:
        df_breakout = pd.DataFrame(breakout_results)
        # Filter to only include valid signals
        df_breakout_valid = df_breakout[df_breakout['is_valid'] == True].copy()
        # Sort by signal strength (descending)
        df_breakout_valid.sort_values(by=['strength'], ascending=False, inplace=True)
    else:
        df_breakout_valid = pd.DataFrame(columns=['symbol', 'signal', 'strength', 'pivot', 'spot_price', 'target', 'stop_loss', 'notes'])
    
    if reversal_results:
        df_reversal = pd.DataFrame(reversal_results)
        # Filter to only include valid signals
        df_reversal_valid = df_reversal[df_reversal['is_valid'] == True].copy()
        # Sort by signal strength (descending)
        df_reversal_valid.sort_values(by=['strength'], ascending=False, inplace=True)
    else:
        df_reversal_valid = pd.DataFrame(columns=['symbol', 'signal', 'strength', 'pivot', 'spot_price', 'target', 'stop_loss', 'notes'])
    
    # Create summary sheet
    summary_data = []
    
    # Add breakout summary
    breakout_buy_count = len(df_breakout_valid[df_breakout_valid['signal'] == 'buy'])
    breakout_sell_count = len(df_breakout_valid[df_breakout_valid['signal'] == 'sell'])
    
    # Add reversal summary
    reversal_buy_count = len(df_reversal_valid[df_reversal_valid['signal'] == 'buy'])
    reversal_sell_count = len(df_reversal_valid[df_reversal_valid['signal'] == 'sell'])
    
    summary_data.extend([
        ['Pivot Strategy Analysis', ''],
        ['Date', datetime.now().strftime('%Y-%m-%d')],
        ['Time', datetime.now().strftime('%H:%M:%S')],
        ['', ''],
        ['Strategy', 'Signal Count'],
        ['Breakout - Buy', breakout_buy_count],
        ['Breakout - Sell', breakout_sell_count],
        ['Reversal - Buy', reversal_buy_count],
        ['Reversal - Sell', reversal_sell_count],
        ['', ''],
        ['Total Buy Signals', breakout_buy_count + reversal_buy_count],
        ['Total Sell Signals', breakout_sell_count + reversal_sell_count]
    ])
    
    df_summary = pd.DataFrame(summary_data)
    
    # Write DataFrames to Excel
    df_summary.to_excel(writer, sheet_name='Summary', header=False, index=False)
    df_breakout_valid.to_excel(writer, sheet_name='Breakout Signals', index=False)
    df_reversal_valid.to_excel(writer, sheet_name='Reversal Signals', index=False)
    
    # If we have the full results (including invalid signals), add them to separate sheets
    if 'df_breakout' in locals():
        df_breakout.to_excel(writer, sheet_name='All Breakout Analysis', index=False)
    if 'df_reversal' in locals():
        df_reversal.to_excel(writer, sheet_name='All Reversal Analysis', index=False)
    
    # Format the Excel file
    workbook = writer.book
    
    # Add formats
    header_format = workbook.add_format({
        'bold': True,
        'text_wrap': True,
        'valign': 'top',
        'fg_color': '#D7E4BC',
        'border': 1
    })
    
    # Format the Summary sheet
    summary_sheet = writer.sheets['Summary']
    summary_sheet.set_column('A:A', 25)
    summary_sheet.set_column('B:B', 15)
    
    # Format the title
    title_format = workbook.add_format({
        'bold': True,
        'font_size': 14,
        'fg_color': '#D7E4BC'
    })
    summary_sheet.write('A1', 'Pivot Strategy Analysis', title_format)
    
    # Format the signal sheets
    for sheet_name in ['Breakout Signals', 'Reversal Signals']:
        if sheet_name in writer.sheets:
            sheet = writer.sheets[sheet_name]
            
            # Apply header format to the first row
            for col_num, value in enumerate(df_breakout_valid.columns.values):
                sheet.write(0, col_num, value, header_format)
            
            # Set column widths
            sheet.set_column('A:A', 15)  # Symbol
            sheet.set_column('B:B', 10)  # Signal
            sheet.set_column('C:C', 10)  # Strength
            sheet.set_column('D:D', 10)  # Pivot
            sheet.set_column('E:E', 10)  # Spot Price
            sheet.set_column('F:F', 10)  # Target
            sheet.set_column('G:G', 10)  # Stop Loss
            sheet.set_column('H:H', 40)  # Notes
    
    # Save the Excel file
    writer.close()
    
    logger.info(f"Strategy results exported to: {file_path}")
    return str(file_path)

def append_to_strategy_log(strategy_results: List[Dict[str, Any]], 
                          strategy_type: str,
                          output_dir: str = DEFAULT_OUTPUT_DIR) -> str:
    """
    Append strategy results to a CSV log file for historical tracking.
    
    Args:
        strategy_results: List of strategy results
        strategy_type: Type of strategy ('breakout' or 'reversal')
        output_dir: Directory to save the CSV file
    
    Returns:
        str: Path to the CSV log file
    """
    # Ensure output directory exists
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Create filename based on strategy type and date
    date_str = datetime.now().strftime("%Y%m%d")
    filename = f"{strategy_type}_strategy_log_{date_str}.csv"
    file_path = output_path / filename
    
    # Filter for valid signals only
    valid_results = [r for r in strategy_results if r.get('is_valid', False)]
    
    if not valid_results:
        logger.info(f"No valid {strategy_type} signals to log")
        return str(file_path)
    
    # Convert to DataFrame
    df = pd.DataFrame(valid_results)
    
    # Check if file exists
    file_exists = file_path.exists()
    
    # Append to CSV
    df.to_csv(file_path, mode='a', header=not file_exists, index=False)
    
    logger.info(f"Appended {len(valid_results)} {strategy_type} signals to log: {file_path}")
    return str(file_path)
