"""
Utility functions for option pricing and symbol formatting.
This module provides functions for calculating theoretical option prices
and formatting option symbols for Fyers API.
"""
import math
import logging
from typing import Dict, List, Any
from datetime import datetime, timedelta

# Configure logging
logger = logging.getLogger(__name__)

def black_scholes_call(S: float, K: float, T: float, r: float, sigma: float) -> float:
    """
    Calculate Black-Scholes price for a call option.
    
    Args:
        S: Spot price
        K: Strike price
        T: Time to expiry in years
        r: Risk-free rate
        sigma: Volatility
        
    Returns:
        float: Call option price
    """
    # Avoid division by zero or negative time
    if T <= 0:
        T = 0.01
        
    d1 = (math.log(S/K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
    d2 = d1 - sigma * math.sqrt(T)
    
    call_price = S * norm_cdf(d1) - K * math.exp(-r * T) * norm_cdf(d2)
    return max(0.01, call_price)  # Ensure minimum price of 0.01

def black_scholes_put(S: float, K: float, T: float, r: float, sigma: float) -> float:
    """
    Calculate Black-Scholes price for a put option.
    
    Args:
        S: Spot price
        K: Strike price
        T: Time to expiry in years
        r: Risk-free rate
        sigma: Volatility
        
    Returns:
        float: Put option price
    """
    # Avoid division by zero or negative time
    if T <= 0:
        T = 0.01
        
    d1 = (math.log(S/K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
    d2 = d1 - sigma * math.sqrt(T)
    
    put_price = K * math.exp(-r * T) * norm_cdf(-d2) - S * norm_cdf(-d1)
    return max(0.01, put_price)  # Ensure minimum price of 0.01

def norm_cdf(x: float) -> float:
    """
    Approximation of the cumulative distribution function for the standard normal distribution.
    
    Args:
        x: Input value
        
    Returns:
        float: CDF value
    """
    # Constants for the approximation
    a1 = 0.254829592
    a2 = -0.284496736
    a3 = 1.421413741
    a4 = -1.453152027
    a5 = 1.061405429
    p = 0.3275911
    
    # Save the sign of x
    sign = 1
    if x < 0:
        sign = -1
    x = abs(x) / math.sqrt(2.0)
    
    # A&S formula 7.1.26
    t = 1.0 / (1.0 + p * x)
    y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * math.exp(-x * x)
    
    return 0.5 * (1.0 + sign * y)

def calculate_theoretical_option_prices(options_data: List[Dict[str, Any]], spot_price: float) -> Dict[str, float]:
    """
    Calculate theoretical option prices using a simplified Black-Scholes model.
    
    Args:
        options_data: List of option data dictionaries
        spot_price: Current spot price of the underlying
        
    Returns:
        Dict[str, float]: Dictionary mapping option symbols to theoretical prices
    """
    result = {}
    
    # Default parameters
    risk_free_rate = 0.05  # 5% risk-free rate
    volatility = 0.20      # 20% volatility
    
    for option in options_data:
        # Skip if no symbol
        if not option.get('call_symbol') and not option.get('put_symbol'):
            continue
            
        strike = option.get('strike', 0)
        if strike <= 0:
            continue
            
        # Get time to expiry in years
        time_to_expiry = option.get('time_to_expiry', 0.0)
        if time_to_expiry <= 0:
            time_to_expiry = 7/365  # Default to 7 days if not available
            
        # Calculate call price
        if option.get('call_symbol'):
            call_price = black_scholes_call(spot_price, strike, time_to_expiry, risk_free_rate, volatility)
            result[option['call_symbol']] = call_price
            logger.debug(f"Theoretical call price for {option['call_symbol']}: {call_price:.2f}")
            
        # Calculate put price
        if option.get('put_symbol'):
            put_price = black_scholes_put(spot_price, strike, time_to_expiry, risk_free_rate, volatility)
            result[option['put_symbol']] = put_price
            logger.debug(f"Theoretical put price for {option['put_symbol']}: {put_price:.2f}")
            
    return result

def format_option_symbol(base_symbol: str, expiry_date: datetime, strike: float, option_type: str) -> str:
    """
    Format option symbol for Fyers API.
    
    Args:
        base_symbol: Base symbol (e.g., 'NIFTY', 'BANKNIFTY')
        expiry_date: Expiry date
        strike: Strike price
        option_type: Option type ('CE' or 'PE')
        
    Returns:
        str: Formatted option symbol
    """
    # Format: NSE:NIFTYYYMDD19000CE
    # YY = year (2 digits)
    # M = month (single digit)
    # DD = day (2 digits)
    year = expiry_date.strftime('%y')
    month = str(expiry_date.month)  # Single digit month
    day = expiry_date.strftime('%d')
    strike_int = int(strike)
    
    # Correct format for Fyers API
    symbol = f"NSE:{base_symbol}{year}{month}{day}{strike_int}{option_type}"
    
    return symbol